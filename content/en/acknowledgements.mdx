# Acknowledgements

The book idea started as a note in April 2020. I asked <PERSON><PERSON><PERSON>, my wife, what do you think about writing a book about debugging CSS? I told her that it will be a very short one (60 pages max). Seven months later, the book has 300 pages. <PERSON><PERSON><PERSON> was the first person to support the book idea, and she insisted that I should move on with this, and here we are. Thank you, my dearest person!

The first person that encouraged me from the community is Mr. <PERSON>. He invited me to talk at Web Directions conference about the book topic and was one of the first supporters. Thank you very much!

I want to thank is <PERSON>. He was helpful and kind enough to proofread the whole book, and highlighting a lot of fixes. Thank you very much!

Finally, I would like to thank <PERSON>, who reviewed the very first draft of the book. He highlighted some important things that I should improve. Thank you, <PERSON>!
