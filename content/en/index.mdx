# Foreword

I've been using, and a big advocate for CSS since before it was even a standard, nearly 25 years.

It's hard to convey what a profound change it brought in developing for the web when introduced, although its widespread adoption took years of the technology maturing in browsers, and of advocacy, changing the long established use of tables for page layout, font elements, and other hacks web developers came up with to design for the Web.

Since then, CSS has matured in ways its originators and early adopters could barely imagine and brings developers incredible power. But this power and complexity come at a cost.

When developing with CSS, I sometimes think of the story of "The Sorcerer's Apprentice" (originally a poem by German Romantic poet <PERSON>, but made famous by <PERSON> in the Disney film Fantasia). The Sorcerer's Apprentice gains access to his wizard master's powers, but unable to wield them correctly, causes mayhem.

Which sounds like a lot of developing with CSS to me!

The Cascade, Specificity, Inheritance are all powerful features of CSS, but also cause many of the problems we associate with the language.

Which is why I'm surprised it's taken so long for someone to really address the significant challenges of debugging CSS. And why I'm excited for <PERSON>'s new book, which addresses this important topic in detail.

I really recommend this to any web developer, it's long overdue!

**<PERSON> — Web Directions**
