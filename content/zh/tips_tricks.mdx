# 通用技巧与诀窍

## 调试多语言网站

当涉及调试多语言网站时，我们需要了解如何测试及排查问题。在讨论多语言网站时，我们将重点关注从左到右（LTR）和从右到左（RTL）布局，分别以英语和阿拉伯语为例进行说明。

### LTR 和 RTL 的常见错误

### 间距问题

在调试 LTR 和 RTL 时，大多数问题与间距相关。每种语言的水平方向会被翻转，间距问题通常表现为内边距或外边距设置不当。例如：

```css
.element {
  margin-left: 10px;
}
```

对于 RTL，它将是这样的：

```css
.element {
  margin-right: 10px;
}
```

我们会对内边距和定位属性（`top`、`right`、`bottom`、`left`）做等效处理。

此外，我们可以使用 CSS 逻辑属性来避免为 RTL 编写更多 CSS。以下是上述示例的外观：

```css
.element {
  margin-inline-start: 10px;
}
```

属性 `margin-inline-start` 是逻辑属性。这意味着，对于 LTR 它等同于 `margin-left`，对于 RTL 则等同于 `margin-right`。若想深入了解 RTL 样式，我推荐你阅读我撰写的[指南](https://rtlstyling.com/)。

### 对齐问题

当文本在 LTR 中向右对齐时，在 RTL 中应相应地调整为向左对齐。

```css
.element {
  text-align: right;
}
```

对于 RTL，它将是这样的：

```css
.element {
  text-align: left;
}
```

### RTL 调试技巧

根据网站的具体实现方式，将页面的 CSS 从 LTR 切换到 RTL 可能非常简单。如果 CSS 已合并到单一文件中，只需在 `html` 元素上设置 `dir` 属性即可完成切换。

```html
<html dir="rtl"></html>
```

我们可以先在 DevTools 中设置该属性，然后检查需要修复的问题。

如果 LTR 和 RTL 的 CSS 不在同一个文件中，通常会分别存放在两个文件中，如 `main-ltr.css` 和 `main-rtl.css`。此时仅切换 `dir` 属性是不够的，还需要修改 `head` 元素中样式表的 `src` 属性。

### 快速添加 RTL 内容的方法

假设我们已经为 LTR 和 RTL 布局构建了 CSS，唯一缺少的是测试 RTL 内容的排版效果。此时，可以使用 Google 的页面内翻译功能快速将 LTR 内容转换为 RTL 语言，帮助我们创建带有实际内容的 RTL 设计，验证文本方向的适应性。

若想了解更多细节，我撰写了题为 [RTL Styling 101](https://rtlstyling.com/) 的详细指南。

## 使用 `@supports`

`@supports` 用于检测用户浏览器是否支持给定的 CSS 功能。

```css
@supports (display: flex) {
  /* If flexbox is supported, apply this. */
  .element {
    display: flex;
  }
}
```

测试 `@supports` 的一个有趣方法是临时禁用其功能。虽然有专门的浏览器扩展可以实现这一点，但我们可以手动添加一个随机字符来破坏规则，从而使 CSS 不生效。

```css
@supports (display: flexB) {
  /* ... */
}
```

我在 `display: flex` 后添加了字母"B"。浏览器无法识别该语法，将退回到默认行为，相当于禁用了 `@supports` 查询。这种方法很实用，对吧？

然而，在包含大量 `@supports` 规则的大型项目中，手动修改显然不切实际。值得庆幸的是，Ire Aderinokun 开发了一个[浏览器扩展](https://github.com/ireade/feature-queries-manager)，支持 Chrome 和 Firefox，能更便捷地管理这些功能查询。

![image-20250325215541817](/img/image-20250325215541817.png)

该扩展将在浏览器 DevTools 中添加一个新标签页。左侧显示嵌套在 `@supports` 查询中的 CSS 功能可切换列表，右侧则列出使用特定功能的每个 `@supports` 查询。上图显示的是与网格相关的 CSS。通过切换左侧的复选框，可以快速禁用或启用 CSS 网格功能，这是测试布局响应性和排查问题的有效方法。

## 浏览器扩展

### Grid Ruler

测试两个 UI 元素是否正确对齐的好方法是使用标尺和参考线。这可以在 Sketch、Adobe XD、Photoshop 和 Illustrator 等设计应用程序中轻松完成。在浏览器中，没有扩展是不可能的。

一个很棒的扩展，[Grid Ruler](https://chromewebstore.google.com/detail/grid-ruler/joadogiaiabhmggdifljlpkclnpfncmj)，仅在 Google Chrome 中可用。它使你能够水平或垂直拖放参考线。这对于验证两个元素是否正确对齐非常有用。

![image-20250325215612101](/img/image-20250325215612101.png)

通过网格线，我们可以直观地验证用户头像和按钮是否正确对齐。

### OLI Grid CSS

[OLI Grid CSS](https://addons.mozilla.org/en-US/firefox/addon/oli-grid-css/) 插件适用于 Firefox 和 Chrome。其优势在于能在页面中绘制网格列线，类似于 Sketch 和 Adobe XD 中的设计辅助线，有助于验证布局是否与设计网格对齐。

![image-20250325215631205](/img/image-20250325215631205.png)

我用 Bootstrap 构建的页面测试了该插件，效果符合预期。需要注意的是，使用前应先确定页面 `.container` 元素的实际宽度。

### Web Developer Extension

![image-20250325215652486](/img/image-20250325215652486.png)

一个非常有用的扩展，提供了很多功能。以下是一些关键功能：

- 禁用所有样式
- 禁用浏览器默认样式
- 禁用内联样式
- 禁用打印样式

这只是 Web Developer 扩展提供的众多 CSS 相关功能中的一部分！

### Pesticide Extension

![image-20250325215708928](/img/image-20250325215708928.png)

正如之前提到的，使用 CSS 轮廓属性是调试设计问题的有效方法。Pesticide 扩展将这一过程简化为单击操作，自动为页面上的每个元素添加随机彩色轮廓线，还能突出显示特定元素。

## 在浏览器中模拟

有时你想通过移动一些元素来快速在浏览器中模拟设计想法。这对于向开发者、客户或设计师展示设计概念很有用。能够快速进行此类编辑对生产力很重要。

利用浏览器的内置工具，我们可以做到这一点。在本节中，我们将专注于在浏览器中快速模拟设计的概念和示例。

### 好老的 CSS 定位

通过 CSS 定位，我们可以在 DevTools 中为元素添加 `position` 属性并调整其位置。这是在测试和调试时快速模拟设计变更的有效方法。

![image-20250325215756080](/img/image-20250325215756080.png)

这里展示了一个带有类别的卡片组件。设计师提出需要调整类别标签的位置，我们可以建议将其移动到左上角。这个修改可以在视频会议中实时完成，只需添加以下 CSS 代码：

```css
.card {
  position: relative;
}
.category {
  position: absolute;
  left: 0;
  top: 16px;
}
```

这种不到一分钟的编辑可以让决策更快地发生。

### 隐藏设计元素

如前所述，在 Chrome 中使用 `H` 键快速隐藏元素是一个实用技巧。通过此功能，我们可以暂时移除某些设计元素，便于截取设计概念的屏幕截图或进行其他调整。

![image-20250325215815868](/img/image-20250325215815868.png)

此处展示了一个部分标题，其中作者头像和姓名的对齐存在问题。设计团队要求临时移除该问题元素。你可以通过直接从 HTML 中删除、使用 `display: none` 隐藏，或在 Chrome 中按 `H` 键快速实现。

### CSS Flexbox

使用 Flexbox，我们可以轻松创建水平或垂直布局。Flexbox 属性如 `align-items` 和 `justify-content` 功能强大，能够实现大多数设计构想。

![image-20250325215837320](/img/image-20250325215837320.png)

该部分标题包含一行 items，但 items 之间的间距不一致。解决方案是添加 `display: flex` 和 `justify-content: space-between`。这一修改会立即生效，且全部在 DevTools 中完成，之后即可截取屏幕截图与团队讨论。

### CSS Grid Layout

这是 CSS 中最强大的布局模块。假设我们有一个特色新闻部分，设计师想要以可呈现的方式布局 items ——比如说，作为等高列。

![image-20250325215857726](/img/image-20250325215857726.png)

我们使用 CSS Grid 布局定义列结构，然后与设计师确认是否符合预期。

```css
.wrapper {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
}
```

如果设计师仍有调整需求，可以继续编辑并实时展示变更效果。此外，你还可以尝试不同的布局方案，为每种方案绑定不同的 CSS 类，通过 DevTools 的".cls"面板快速切换查看效果。

### CSS 视口单位

视口单位使我们能够创建占据视口完整水平或垂直空间的元素，也可用于字体大小的动态调整。这些用例为设计提供了更大的灵活性和响应性。

假设我们需要创建一个占据屏幕 90% 高度的英雄区域。为快速验证设计需求，我们可以通过以下方式快速模拟：

```css
.hero {
  height: 90vh;
}
```

我们给英雄部分一个 `90vh` 的高度，这将使它占用屏幕垂直空间的 90%。我们在不到一分钟内完成了这个编辑！

### CSS 列

如果希望比 CSS Grid 更快速地实现等宽列布局，可以使用 CSS Columns。例如，将页脚链接分为两列只需一行代码，修改后即可立即向设计师展示效果：

```css
.footer-section {
  columns: 2;
}
```

CSS Columns 的另一个优点是可以通过键盘上下箭头键动态调整列数。

![image-20250325215935175](/img/image-20250325215935175.png)

### CSS 滤镜

假设设计师希望为网站实现暗色模式，但尚未准备设计稿。利用 CSS 滤镜，我们可以快速模拟暗色效果。

```css
html {
  filter: invert(90%) hue-rotate(25deg);
}
```

为优化显示效果，我们需要恢复图像、视频等不应被反转的元素：

```css
html img,
html video,
html iframe {
  filter: invert(100%) hue-rotate(-25deg);
}
```

完成上述调整后，我们可以截取全页截图并即时分享给团队——整个过程不到两分钟！这种方式不仅高效，还节省了设计师的宝贵时间，因为手动创建暗色模式设计稿通常需要至少 10 分钟。

### 去饱和设计

使用 CSS 滤镜将页面去饱和（转换为黑白）是一个实用技巧，原因如下：

- 如果你正在测试的网站颜色很重，你的眼睛可能会疲劳。去饱和页面将帮助你专注于修复手头的错误。
- 它对测试和探索很有用。当页面饱和时，你可以轻松发现任何不适合设计的颜色。
- 可访问性测试更加直观。灰度模式能清晰展现文字与背景的对比度，帮助判断可读性。

要将网页去饱和，打开浏览器 DevTools，选择 `html` 或 `body` 元素，并添加以下样式：

```css
html {
  filter: grayscale(1);
}
```

就是这样。你现在有一个黑白网站！

### 线框样式

在快速模拟设计概念时，可能没有足够时间调整颜色和字体。此时，可以通过简单 CSS 将整个页面转换为线框样式，帮助快速验证布局结构并获取反馈。

实现方法如下：

```css
* {
  color: #000;
  background: #ccc !important;
  outline: solid 1px;
}

img,
video,
iframe {
  background: #ccc;
  opacity: 0;
}
```

## 触摸屏的悬停

在触摸设备（手机、平板电脑等）上调试时，可能会发现某些元素在滚动时意外改变颜色或样式。这是因为 `:hover` 样式在触摸屏上被错误触发。解决方案是使用 `hover` 媒体查询。根据 [Mozilla Developer Network](https://developer.mozilla.org/en-US/docs/Web/CSS/@media/hover)：

> hover CSS 媒体功能可用于测试用户的主要输入机制是否可以悬停在元素上。

![image-20250325220020774](/img/image-20250325220020774.png)

```css
@media (hover: hover) {
  .element:hover {
    color: #222;
  }
}
```

这样可以确保 `:hover` 样式仅在支持悬停操作的设备上生效。目前主流浏览器均支持此功能。在 Chrome 设备模式下测试时，浏览器会将当前视图识别为触摸屏，便于验证 `hover` 媒体查询的效果。

## 使用 CSS 显示潜在错误

没有直接的方法在 CSS 中显示潜在错误。但是，一些聪明的人设计了解决方法，使我们能够调试 HTML 和 CSS 的不正确使用。让我们探索其中一些。

### 在上下文之外使用 CSS 类

假设你与团队构建了设计系统，需要检查组件的不正确使用情况。在 Harry Roberts 的倒三角 CSS（ITCSS）方法论中，他通过以下方式创建 CSS 类使用错误的视觉提示：

```html
<div class="o-layout">
  <div class="o-layout__item"></div>
  <div class="o-layout__item"></div>
  <div class="o-layout__item"></div>
</div>
```

`.o-layout` 类用于充当布局包装器的元素。`.o-layout__item` 类应该只应用于具有 `.o-layout` 类的父元素内的元素。以下用法将是不正确的：

```html
<div>
  <div class="o-layout__item"></div>
</div>
```

`.o-layout__item` 类的元素不应独立存在。通过以下 CSS 规则，我们可以直观地识别此类错误用法：

```css
.o-layout__item {
  /* Show a warning outline by default. */
  outline: solid 5px yellow;
}
.o-layout .o-layout__item {
  /* Remove the outline when item is in .o-layout. */
  outline: none;
}
```

此外，我们可以检测 `.o-layout__item` 是否是 `.o-layout` 的直接子元素。

```css
.o-layout > :not(.o-layout__item) {
  outline: solid 5px yellow;
}
```

### 向元素添加 `width` 或 `height` 属性

通常情况下，除 `img` 元素外，不建议为其他 HTML 元素直接设置 `width` 和 `height` 属性。

```css
:not(img):not(object):not(embed):not(svg):not(canvas)[width],
:not(img):not(object):not(embed):not(svg):not(canvas)[height] {
  outline: solid 5px red;
}
```

进一步，你可以使用 Gaël Poupard 的浏览器扩展，[a11y.css](https://ffoodd.github.io/a11y.css/index.html)，它显示不同的建议、警告和错误。
