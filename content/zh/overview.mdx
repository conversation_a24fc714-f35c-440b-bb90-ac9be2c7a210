# 引言与概述

说实话，CSS 调试并不是一件轻松的事，因为我们并没有一套直接又清晰的方法来处理 CSS 问题。

与传统编程语言（如 Java、C 和 PHP）相比，这些语言的调试技术已经发展多年并趋于成熟。但 CSS 的情况却不同。调试 CSS 与调试编程语言不同，因为你在编译时不会收到错误提醒。相反，你会遇到静默错误，这对解决问题毫无帮助。

在调试 CSS 错误之前，你首先需要发现它。有时，你可能会收到同事的报告，说有一个 bug 需要修复。由于没有直接的方法可以遵循，找到 CSS bug 可能很困难。即使对经验丰富的 CSS 开发者来说，调试和发现 CSS 问题也可能是一件棘手且令人困惑的事情。

本章将讨论：

- CSS 调试的发展历史
- 当今的变化
- CSS 调试的含义
- 调试思维
- 为什么调试需要时间
- 本书主题概览

## CSS 调试的发展历史

由于本书专注于调试和查找 CSS 问题，你应该了解一些 CSS 调试工具多年来是如何发展的。

### Style Master

你可能会惊讶地发现，第一个 CSS 调试工具是在 1998 年发布的 —— 距今已有 22 年之久（本书写作于 2020 年）！它的创造者是 John Allsopp 和 Maxine Sherrin，他们将其命名为 Style Master。正如他们描述的那样：

> Style Master 是领先的跨平台 CSS 开发工具。它不仅仅是一个文本编辑器，Style Master 支持你的工作流程，包括：基于 HTML 创建样式表；实时编辑 PHP、ASP.NET、Ruby 等动态生成网站的 CSS；通过 FTP 编辑 CSS；以及更多功能。

![style master](/img/1-1.png)

Style Master 的目标是让 CSS 工作更加高效、更有生产力且更愉快。正如你所看到的，CSS 调试多年来一直是开发者关注的话题。

### Firebug 浏览器扩展

2006 年，Joe Hewitt 发布了 [Firebug](https://getfirebug.com/) 的第一个版本。

> Firebug 是一个已停止维护的免费开源 Mozilla Firefox 浏览器扩展，它提供了对网站 CSS、HTML、DOM、XHR 和 JavaScript 的实时调试、编辑和监控功能。 —— [维基百科](<https://en.wikipedia.org/wiki/Firebug_(software)>)

![firebug](/img/1-2.png)

Firebug 的主要功能与今天的开发者工具（DevTools）非常相似，包括：

- 检查 HTML 和 CSS
- 查看 JavaScript 控制台
- 测试网页性能

如果没有这些优秀工具（Style Master 和 Firebug）创造者的努力，我们今天可能就没有现在使用的开发者工具。

## 当今的变化

如今的场景已经发生了巨大变化。现在每个浏览器都内置了开发者工具，让开发者可以轻松检查和编辑网页的 HTML、CSS 和 JavaScript。在本书中，我们主要关注 CSS。

值得一提的是，当 Style Master 和 Firebug 发布时，网站结构非常简单，我们只需要测试一种屏幕尺寸。如今，一个网站可以被数百种设备访问，包括智能手表、手机、平板电脑、笔记本电脑和台式机。为所有这些类型的设备进行调试并非易事。你可能修复了移动端的问题，却无意中破坏了桌面端。

这不仅仅是屏幕尺寸的问题。在过去 10 年中，Web 项目的规模也变得更大。例如，像 Facebook 或 Twitter 这样的大型前端项目的开发者需要一种系统化的方式来测试和调试。所有这些对 Web 开发工作的变化都清楚地表明，调试必须从项目第一天开始就被重视，开发者必须将其作为核心技能来学习。

## CSS 调试的含义

> 调试是查找和修复计算机程序、软件或系统中缺陷（阻碍正常运行的问题或缺陷）的过程。 —— [维基百科](https://en.wikipedia.org/wiki/Debugging#Debugging_process)

我们可以使用相同的定义。查找和修复 CSS bug 是一项重要技能。无论你习惯使用哪种编程语言，CSS 的调试步骤几乎都是相同的。本章后面部分，我们将介绍一个清晰的调试策略，你可以立即使用。

当我在本书中提到 "CSS bug" 时，我指的是由开发者造成的 bug，而不是浏览器本身的 bug。但我们也会讨论这两种类型。

## 为什么应该教授调试

浏览器开发者工具的快速发展使得很难跟上所有 CSS 调试的技术和方法。更不用说缺乏一个组织良好的、便于初学者跟随的资源。

## 调试思维

根据 Devon H. O'Dell 在 "[The Debugging Mindset](https://queue.acm.org/detail.cfm?id=3068754)" 中的研究：

> 软件开发人员花费 35-50% 的时间验证和调试软件。调试、测试和验证的成本估计占软件开发项目总预算的 50-75%，每年超过 1000 亿美元。

如果你需要快速修复 bug，你可能会感到有点压力，这可能导致你在没有明确策略的情况下急于求成。这很容易导致混乱，在无关紧要的事情上浪费时间。

任何编程语言都有逻辑性和非逻辑性错误。以 JavaScript 为例。当你的 JavaScript 出现错误时，你可以通过检查浏览器控制台看到。至少你会得到错误发生的证据和原因。

CSS 完全不同。当你犯错时，你不会收到任何类型的提醒。仅这一点就使得最简单的 CSS bug 很难修复，除非你思路清晰并遵循明智的策略。CSS bug 可能是由开发者造成的，比如 CSS 属性使用不当，也可能是由于浏览器之间的不一致性造成的。

此外，你可能需要负责测试网站并发现 bug。所以，我们不仅要处理修复 bug，还要发现它们。

### 识别 CSS Bugs

在客户或团队成员发现网站问题之前，你可以主动进行一些测试，尝试故意**破坏**设计。在第五章中，你将学习一些故意破坏 CSS 布局的方法。

### 向他人解释 Bug

你是否曾经花费数小时试图解决 CSS 问题，结果在向朋友或同事解释时，突然灵光一闪想到了解决方案？这就是向朋友解释 bug 的效果。你陷入困境是因为你没有花足够的时间深入思考问题。

当你发现自己陷入这种循环时，休息一下，稍后再回来。修复问题需要高度集中。如果你在精神疲惫的状态下工作，你可能会无意中破坏其他东西。通过休息来避免这种情况。

## 为什么调试需要时间

在 Matt Lacey 的优秀博客文章 "[You've Only Added Two Lines — Why Did That Take Two Days!](https://www.mrlacey.com/2020/07/youve-only-added-two-lines-why-did-that.html)" 中，Matt Lacey 提出了一些关于为什么调试耗时的可靠理由。让我们逐一分析。

### 问题不清晰

不要期望某人能详细报告问题。模糊的描述是可以的。在要求更多细节之前，尝试尽可能全面地理解问题。一旦你做到了这一点，你需要在你的机器上复现 bug，然后你就有了一个起点。

### 症状比原因更容易处理

在处理问题时，重要的是调查问题的原因，而不仅仅是症状。正如 Lacey 所说：

> 如果某些代码抛出错误，你可以简单地用 try..catch 语句包裹它并抑制错误。没有错误，没有问题。对吗？抱歉，对我来说，让问题隐形和修复它不是一回事。

用"快速修复"让 bug 隐形可能会引入一些意想不到的副作用。你有机会修复问题，而不是制造更多问题！

### 专注于问题的单一路径

某些问题可以通过多种方式复现，而不仅仅是报告的方式。找到这些方法不仅有助于彻底解决问题，还能深入了解 CSS 的编写方式，以及代码库中是否还有其他地方可能出现相同问题。这对于在 bug 影响用户之前修复它们非常有帮助。

### 忽略副作用

修复问题是一回事；避免修复问题带来的副作用是另一回事。这就是为什么最好用最少的 CSS 修复问题，并对可能的副作用有充分的理解。

## 编写易于调试的代码

组织不当的代码会使调试变得更加困难。对于大型 Web 项目，CSS 应该按组件和功能模块拆分成多个文件，然后通过 CSS 预处理器（如 Sass、LESS 或 PostCSS）进行编译。

如果你决定将所有 CSS 写入单个文件，不要期望调试会很容易。你最终会在大文件中无休止地上下滚动。这种方法令人困惑且不理想。单文件 CSS 中往往会出现更多 bug。

在下一章中，我们将深入探讨不同类型的 CSS 问题，详细了解浏览器中的调试，以及更多内容。

## 本书面向的读者

本书适合希望提升 CSS bug 查找与修复技能的设计师、前端和后端开发者。你应该具备中级水平的 HTML 和 CSS 知识。

在某些章节中，你需要按照步骤安装 npm 包。不用担心，这不需要深入的 Node.js 经验。你可以轻松地跟随操作。

## 为什么我写这本书？

缺乏学习如何查找和修复 CSS bug 的资源是我写这本书的主要原因。当我开始研究这个主题时，我发现这个主题一直被忽视。应该有一本指南以简单直接的方式讨论所有与调试相关的细节。

## 本书章节概览

以下是本书各章节的概览：

- CSS bug 简介
- 调试环境和工具：浏览器开发者工具、虚拟机、移动浏览器
- 经常导致 bug 的 CSS 属性
- 故意破坏布局
- 浏览器不一致性和实现 bug
- 通用技巧和窍门

现在我们已经完成了本书的介绍，让我们开始调试 CSS 吧！
