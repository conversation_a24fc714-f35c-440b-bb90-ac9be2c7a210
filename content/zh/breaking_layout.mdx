# 不同方式破坏布局

这整本书都是关于发现 CSS 问题并解决它们。我们能做相反的事情吗？在本节中，我们将探索破坏 CSS 布局并使其失败的不同方法。是的，你没看错。

## 添加长文本内容

正如我们所见，布局错误的一个常见原因是文本比预期的长。随机添加长文本可以揭示你没有想到的 CSS 问题。

![image-20250325213047635](/img/image-20250325213047635.png)

第一个框中的文章标题很短；设计师设计时是为了让它适合。开发者复制了文本并基于此实现了组件。当我尝试添加长文本时，发生了有趣的事情！链接图标被推到了新行。

我们不能假设这是一个错误，但我们可以问自己几个问题：

- 这种行为是故意的吗？也就是说，当文本变长时，它应该将其他元素推到新行吗？
- 或者这是意外的，根本不应该发生？

一些 CSS 问题是由于设计师和开发者之间的误解而发生的。设计师没有制定所有可能的场景，开发者没有想到询问有关组件的问题。双方都有错。

### forceFeed.js

幸运的是，存在帮助我们添加随机内容并测试问题的工具。[forceFeed.js](https://github.com/Heydon/forceFeed) 就是其中之一。让我们了解它是如何工作的。

#### 安装

首先，通过 `npm` 或 `bower` 安装它：

```bash
npm install forcefeed
# or
bower install forcefeed
```

或者简单地从 GitHub 存储库下载 JavaScript 文件。

#### 包含脚本

在页面内容之后和 body 元素结束之前包含脚本。

```html
  <script src="path/to/forceFeed.js"></script>
</body>
```

#### 向元素添加属性

向你想要添加随机内容的任何元素添加 `data-forcefeed` 属性。

```html
<div class="person">
  <h3 class="name" data-forcefeed="words|2"></h3>
  <p class="description" data-forcefeed="sentences|3|6">This will be overridden</p>
</div>
```

第一个，`data-forcefeed="words|2"`，将根据定义的数组生成两个随机单词，`data-forcefeed="sentences|3|6"` 将生成三到六个之间的随机句子数。

#### 添加数组

```js
window.words = ['Design', 'Work', 'Awesome', 'Cool'];
window.sentences = [
  'Can you break me?',
  'I love food and baking',
  'How are you today?',
  'When was the last time you saw mom?',
];
```

#### 执行脚本

最后，我们需要让脚本在页面上工作。

```js
forceFeed({ words: window.words, sentences: window.sentences });
```

这样，我们现在可以刷新页面（`Command` 或 `Control` + `R`）来查看内容更改。也许你会注意到一个破损的元素。

## 尝试不同语言的内容

如果你正在开发多语言网站，那么当一些设计组件有不同内容时，它们很可能会破坏。

![image-20250325213249095](/img/image-20250325213249095.png)

我们可能有一个修改了字距（字符之间的间距）的英文标题。它可能工作得很好，但当翻译页面在从右到左（RTL）模式下有阿拉伯语内容时，文本会破坏。阿拉伯语没有字距这样的东西。

当我们假设按钮组件的特定最小大小时，可能会出现另一个错误。当内容被翻译成另一种语言时，它可能看起来不同。

![image-20250325213310756](/img/image-20250325213310756.png)

在 Twitter 上，"Done" 按钮在英语中看起来很好。在阿拉伯语中，按钮看起来太小，不容易注意到。原因是按钮有 `min-width: 40px` 的规则。这可以通过增加按钮的最小宽度来修复。

这些与语言相关的错误很重要，不应该被忽略。如果你有兴趣了解更多关于这个问题的信息，我写了一个完整的指南，[RTL Styling 101](https://rtlstyling.com/)。

## 调整浏览器窗口大小

这是破坏布局并揭示其缺陷的最简单方法之一。当你调整浏览器窗口大小时，你会看到一些你通常不会注意到的问题。一个有趣的关注领域是我称之为"中间"设计案例。我在我的博客上写了一篇[详细文章](https://ishadeed.com/article/in-between/)，我想在这里再次回顾这些概念。

> 在响应式网页设计中，通常在页面的不同变体上工作。典型的网页至少应该有两个变体，一个用于小屏幕尺寸（例如移动设备），另一个用于大屏幕（例如桌面）。很多时候，我们忘记了 `中间` 设计变体，我们最终得到一个组件或部分太宽或太窄。

换句话说，当你测试中间设计状态时，你会发现更多问题。相信我，你会发现一些你或团队没有考虑过的有趣问题。

![image-20250325213407658](/img/image-20250325213407658.png)

这里我们有一些卡片，需要在移动设备上是一列，在平板电脑上是两列。中间状态使卡片看起来太宽，这可能影响可读性。虽然这可能看起来不像缺陷，但它是。

测试中间状态重要性的另一个明确例子是以下页脚设计，取自真实项目。

![image-20250325213427906](/img/image-20250325213427906.png)

在中间视图中，Instagram 的社交媒体图标换行到新行。这种行为是不期望的，根本不应该发生。

这应该足以说明这样的问题不能被忽略。由于浏览器调整大小的简单技巧，我们可以相当容易地发现它们。

## 避免占位符图像

图像在使网页易于访问和阅读方面发挥重要作用。作为前端开发者，你的工作是为可以处理任何使用的图像的组件提供可靠的结构。例如，你可能正在开发一个具有完美封面图像和伴随文本的英雄部分。

![image-20250325213514475](/img/image-20250325213514475.png)

破坏这样的组件很容易——只需更改图像。突然，你会看到文本很难阅读。我们忘记了放置一个半透明的黑色覆盖层，这会使文本易于阅读。不幸的是，一些设计师假设他们设计的实现将完全匹配他们的模型。情况并非如此。在开发过程中会发生很多变化。

通过更改图像并在它们上尝试不同的样式，我们可以发现隐藏的问题。

图像大小和尺寸是另一个值得关注的领域。作为前端开发者，你可能准备一个可用于食谱和文章等内容中图像的媒体组件。要提出的一个问题是：期望或推荐什么图像尺寸？内容管理者应该被限制为向内容管理系统（CMS）上传预定义大小的图像，还是应该自由上传他们喜欢的任何大小？

![image-20250325213547667](/img/image-20250325213547667.png)

我们这里有一个包含图像的媒体组件。第一个是设计师提供的默认图像，第二个是作者添加到 CMS 的。这种不一致性不好。设计师和开发者应该就图像大小达成一致，然后教作者遵循该标准。

如果你对图像大小没有太多控制，那么我建议使用 CSS `object-fit` 和图像的固定高度。这可以保持所有图像在相同高度内而不破坏它们。

```css
.media__thumb {
  height: 220px;
  object-fit: cover;
}
```

## 在 Internet Explorer 中打开

不，这不是玩笑。Internet Explorer 以破坏网站而闻名。如果你的网站需要在 Internet Explorer 中工作，那么你需要考虑你做出的每个 CSS 决定。例如，如果你使用 CSS grid 进行布局，那么建议使用 `flexbox` 或更旧的布局方法（`floats`、`inline-block` 等）添加后备。

## 在纵向和横向方向之间旋转

在更新我个人网站的移动菜单时，我发现了一个有趣的设计问题。像将设备从纵向旋转到横向这样简单的事情可以揭示意外问题，特别是如果一些元素是固定或绝对定位的。

![image-20250325213701039](/img/image-20250325213701039.png)

"关闭"按钮是绝对定位并水平居中的。在横向方向上，按钮与导航重叠，这显然不是预期的。在两个方向上测试都很重要。

## 总结

在本章中，我们学习了如何故意破坏布局。接下来，我们将探索浏览器不一致性和实现错误。
