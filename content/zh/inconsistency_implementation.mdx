# 浏览器不一致和实现缺陷

我们都知道网络浏览器存在不一致性，这很正常。作为网络开发者，我们必须在项目的第一天就修复这些问题，这样我们就可以从一个干净和可靠的代码基础开始。在本章中，我将介绍最常见的 CSS 重置方案，以及如何制作简化测试用例和进行回归测试。

## 使用 CSS 重置文件

CSS 重置文件是网络开发的重要组成部分。最常见的两个是 Eric Meyer 的 Reset CSS 和 Nicolas Gallagher 的 Normalize.css。

通过使用 CSS 重置文件，你将节省大量时间来修复和调试已经修复的问题。以下是来自 Normalize.css 的示例：

```css
/**
  * 1. Correct the line height in all browsers.
*/
html {
  line-height: 1.55; /* 1 */
}
```

在所有浏览器中拥有一致的 `line-height` 很重要。它将节省你弄清楚为什么 `line-height` 在浏览器中不一致工作的时间。

另一个例子是使诸如 `b` 和 `strong` 之类的元素具有粗体字重。这在所有浏览器中不一致工作，所以添加它将防止意外行为。

```css
/**
  * Add the correct font-weight in Chrome, Edge, and Safari.
*/
b,
strong {
  font-weight: bold;
}
```

这些只是几个例子。记住包含 CSS 重置文件。如果你不想，那么至少制作你自己的重置文件。有些人会争论重置文件并不总是需要的，因为它会增加 CSS 文件的总大小。我同意。但你可以轻松创建自己的小文件并完成它。

## 使用 Normalize.css

与其他一些重置相比，Normalize.css 只修复浏览器一致性问题，而不重置所有内容。它将保持网络浏览器之间的常见 CSS 样式。例如，诸如 `h1` 和 `h2` 之类的标题元素的边距将被保留。

这是来自 Normalize.css 的一个片段，重置 body 元素的边距：

```css
body {
  margin: 0;
}
```

根据你正在工作的项目的性质，你可以决定最好使用什么。

## 浏览器实现缺陷

网络浏览器是由人类制作的，人类会犯错误。发现浏览器不按预期支持特定功能或与其他浏览器不同地实现功能是完全正常的。在本节中，我们将介绍在浏览器实现中发现缺陷的步骤。

首先，什么是浏览器实现错误？它是由浏览器本身引起的错误。错误可能在一个或多个浏览器中，是由 CSS 规范的不当实现引起的。

### 验证缺陷

真的有缺陷，还是你弄错了？开始调试某些东西，只是后来意识到它不是缺陷，而是故意的功能，这是浪费时间的。

一些错误是在所有设备上的所有浏览器中出现的——这些很容易找到。其他错误出现在特定浏览器或设备中，如 Nexus 5 Android 手机——找到这些更困难，因为你需要实际的 Nexus 5 设备或在线模拟器，这通常不是免费的。

验证缺陷确实是缺陷将根据其复杂性而变化。一旦你确定它是一个缺陷，那么你将进入下一步。

### 决定正确的行为

一旦你验证了它是一个缺陷，你需要决定功能应该如何看起来和行为。例如，你可以决定特定元素的高度应该在 150 到 450 像素之间，如果它超过了，那么它将被认为是一个缺陷。

### 隔离缺陷

一旦你验证了缺陷并决定了正确的行为，尝试重现它。让我们学习如何通过测试用例简化来重现缺陷。

## 测试用例简化

网络开发者中最被低估的技能之一是创建简化的测试用例。当我们在构建的网页中遇到问题时，我们需要识别原因。问题可能跨浏览器或仅在移动浏览器上。通过使用页面的整个 HTML 和 CSS 来调试问题不是理想的。我们需要在测试用例中隔离问题，这样我们的时间更多地花在修复问题上，而不是识别问题上。

让我们学习如何制作简化的测试用例。

1. **禁用 JavaScript**

如果你禁用 JavaScript 并且问题仍然存在，那么你会知道问题与 JavaScript 无关。这有助于快速排除与 JavaScript 相关的问题。此外，你可以在私人模式标签中打开项目，或停用所有浏览器扩展。如果问题消失，那么可能是因为浏览器扩展。

2. **识别问题**

问题与 alignment 有关吗？还是 horizontal flow 有关？无论是什么，我们需要识别它。非常精确地表达问题有助于："我将调试英雄部分的水平滚动。"

3. **隔离 HTML**

打开浏览器的 DevTools 并梳理页面的 `head` 元素，消除任何不需要的样式和脚本文件。如果某些东西看起来不相关，删除它并继续。一旦 `head` 被清理，移动到 `body` 元素并删除所有与问题无关的 HTML 元素。

4. **隔离 CSS**

现在我们确信 HTML 只包含我们想要调试的代码位，让我们隔离该 HTML 所需的 CSS。删除任何装饰性样式，如 `color` 和 `background-color`。保持 CSS 尽可能最小，只留下导致问题的 CSS 规则。

5. **注释代码**

如果你怀疑特定的 HTML 元素或 CSS 规则导致问题，添加注释解释，这样你在调试时不会忘记。当你需要从其他人那里获得帮助或作为提醒，如果你在一段时间后回到问题时，注释非常有用。

6. **使用简化的文件**

一旦我们完成隔离问题，让我们将剩余的 HTML 和 CSS 复制到新的本地文件，然后我们就准备好了。我们现在有了问题的简化测试用例。更好的是，我们可以将 HTML 和 CSS 添加到 [CodePen](https://codepen.io/)，这将使我们自己测试或从其他人那里获得帮助变得容易。

### 简化测试用例的示例

为了使事情更清楚，让我们通过一个 CSS 缺陷的真实示例，看看如何将其转换为简化的测试用例。

在下图中，我们有一个具有水平滚动问题的网页。我们已经努力尝试，无法弄清楚如何解决问题。所以，让我们尽可能地隔离问题。

![image-20250325214220882](/img/image-20250325214220882.png)

首先，我们将尝试以下操作：

1. 从页面的 `head` 元素中删除对演示不重要的任何额外样式和脚本。

2. 检查 `body` 元素，逐个删除部分。如果删除部分导致问题消失，那么保留它。

3. 一旦我们决定了应该保留的 HTML，下一步是 CSS。我们只需要使演示工作所需的 CSS，如 `width`、`height` 和 `display`。如果装饰性属性如 `background`、`border`、`color` 和 `box-shadow` 不影响问题，可以删除它们。

按照上述步骤，我们应该有**最少**的 HTML 和 CSS 来使测试用例工作。重复上述步骤，确保代码是干净的。这是我们隔离后简化测试用例的外观：

![image-20250325214259696](/img/image-20250325214259696.png)

看起来问题在 grid 部分。现在，问题是，是否可能进一步简化这个？这是我们可以做的：

- 删除网格中的第二行。
- 清理卡片的圆角和阴影。
- 从每张卡片中删除标题。

![image-20250325214316550](/img/image-20250325214316550.png)

一旦清理完毕，我们可以添加一些注释来帮助测试。

```css
/* Not sure if 100vw is causing the horizontal scrolling. */
.section {
  width: 100vw;
  padding: 1rem;
}
```

这个注释对我们或任何其他尝试修复错误的人都有帮助。这是我们可以做的最多的来简化测试用例。下一步是提取 HTML 和 CSS，并将它们上传到我们的网站或我们想要的任何地方。

## 使其失败

在他的调试书中，Dave Agans 将"使其失败"确定为调试任何编码问题的主要步骤之一。我们能够在 CSS 中遵循这个建议的程度将取决于我们正在处理的错误类型。一些错误很明显——例如，在所有视口大小中出现的错误。其他错误类型更复杂，在这种情况下使它们失败更困难。

正如 Agans 在他的书中提到的，使其失败的原因是这样我们可以：

- 看到错误，
- 专注于原因，
- 告诉我们是否已经修复了它。

他的一句话说明了我们经常遇到的误解：

> 吐司只有在你把面包放在烤面包机里时才会烧焦；因此问题出在面包上。

有时，我们误解了 CSS 问题的原因。吐司只有在我们把它放在烤面包机里很长时间时才会烧焦。如果它烧焦了，那是我们的错，不是烤面包机的错。这同样适用于 CSS 开发。如果我们使用布局模块来做它不是为之设计的事情，那么错误是我们的。

你可能想知道，"如果我已经尝试了我知道的一切，仍然无法重现问题怎么办？"好吧，提醒自己每个失败都有原因。没有找到它的秘密配方；它隐藏在随机性的某个地方。

## 备份你的工作

在测试之前，将你的工作保存在新的 Git 分支中。如果你不使用版本控制，将你的工作复制到备份中，并从那里开始测试。通过做其中之一，迭代和更改事物将更安全，丢失工作的机会将非常低。

## 记录一切

对于复杂的 CSS 错误，我喜欢写下以下内容：

1. 我做了什么，
2. 我采取的步骤顺序，
3. 采取步骤的结果发生了什么。

记录这些步骤对你和你的团队都有帮助。

以下是记录重现 CSS 错误步骤的示例。

1. 在 iOS 12 上的 Safari 中打开网站。
2. 点击移动菜单切换。
3. 点击关闭按钮。它不工作。
4. 再次点击关闭按钮。它工作。

在执行上述步骤时，页面应该是空白的，除了存在问题的标题。

## 测试和迭代

一旦我们有了简化的测试用例，我们可以开始在相关浏览器或设备中测试错误。我们会继续迭代和编辑，直到我们注意到差异。

在迭代时，**一次更改一件事**非常重要。不要随机更改 CSS 并希望它会工作。如果它工作，那么你不会知道你是如何做到的，猜测工作将开始。更改一件事，测试，重复。

这种方法的有用性在于，当错误被修复时，我们可以**比较**我们所做的更改以使其工作。如果我们一次更改一百万件事来使其工作，这不会发生。

## 研究问题

如果你已经努力尝试修复问题但无法做到，那么互联网是你的朋友。在线搜索问题或模式，看看其他人是否面临同样的问题。你不是第一个的机会很高。

## 向浏览器供应商报告

如果你相信你发现的错误是独特的，没有人遇到过，那么是时候向浏览器供应商报告了。每个浏览器供应商都有一个公共论坛，包含用户提交的所有错误。如果你已经记录了重现问题的步骤，如前面建议的，那么你只需要发布步骤和你的简化测试用例文件。此外，如果有帮助，提交截图或视频。

这是在浏览器实现中提交错误的地方：

- Firefox: [bugzilla.mozilla.org](https://bugzilla.mozilla.org)
- Safari: [bugs.webkit.org](https://bugs.webkit.org)
- Chrome: [bugs.chromium.org](https://bugs.chromium.org)

## 永远不要丢弃调试演示

在制作简化测试用例时，你可能会创建它的多个副本，每个都有轻微的更改。不要删除它们。存档它们，因为它们在将来可能有帮助。你可以用它们做几件事：

- 写一篇关于错误和你的测试用例的博客文章，解释你如何修复它。
- 将它们作为你自己的日志保留，当你面临类似错误时。
- 与想要学习你如何修复它的同事或团队成员分享它们。

## 回归测试

如 [Wikipedia 上解释的](https://en.wikipedia.org/wiki/Regression_testing)：

> 回归测试是重新运行功能和非功能测试，以确保先前开发和测试的软件在更改后仍然执行。如果不是，那将被称为回归。

当你修复错误时，你可能会意外地破坏另一件事而不知道。这被称为回归。测试回归可能很耗时，因为错误可能在特定环境、视口大小或滚动位置中发生。使用工具，我们可以通过定义我们的设计组件来进行回归测试。

在本节中，我们将学习如何使用 [BackstopJS](https://github.com/garris/BackstopJS) 进行回归测试。根据[官方 GitHub 存储库](https://github.com/garris/BackstopJS)：

> BackstopJS 通过比较随时间的 DOM 截图来自动化你的响应式网络 UI 的视觉回归测试。

BackstopJS 使用无头 Chromium 浏览器，与 Google Chrome 使用的相同。它的工作原理如下：

1. 为我们想要测试的页面分配 URL 路径。
2. 添加我们想要观察的选择器。
3. 为它们生成参考截图。
4. 运行 `backstop test` 来测试我们的更改与参考截图。

让我们学习如何使用 BackstopJS。首先，全局安装它：

```bash
$ npm install -g backstopjs
```

在我们项目的目录内，我们需要初始化一个 BackstopJS 项目。

```bash
$ backstop init
```

### BackstopJS 配置文件

当我们初始化项目时，将在项目的根目录中创建一个 `backstop.json` 文件。你会在那里找到可以配置的所有内容。对于我们的简单课程，需要以下内容：

- `id`
- `viewport`
- `scenarios`

在 `scenarios` 数组中，有一个 `selectors` 数组，我们可以在其中添加所有要观察的 CSS 选择器。我添加了以下内容：

```json
"viewports": [
  {
  "label": "phone",
  "width": 320,
  "height": 480
  },
  {
  "label": "tablet",
  "width": 1024,
  "height": 768
  }
],
"scenarios": [
  {
    "url": "http://localhost:8080/",
    "selectors": [
      ".c-header--full"
    ]
  }
]
```

在这个配置中，我们添加了两个要观察的元素，页面的 URL 是 `index.html`。下一步是生成**参考**截图。

```bash
$ backstop reference
```

通过为我们分配的元素生成参考，我们可以在 CSS 中进行更改。如果与参考有什么不同，BackstopJS 将抛出错误，并提供详细的 UI。

要使用该工具，让我们增加标题中的垂直内边距，然后重新运行测试。

```bash
$ backstop test
```

![image-20250325214826708](/img/image-20250325214826708.png)

测试失败，因为参考截图与测试的截图不同。注意第三个截图如何有粉红色——这是差异的突出显示。

在真实项目中，我们可能会修复问题，在过程中无意中创建回归。我们无法在大型项目中手动测试所有内容，所以拥有这样的工具使我们的生活更轻松，使我们更有生产力。

## 总结

在本章中，我们学习了 CSS 重置、简化测试用例和回归测试。我们几乎完成了！在下一章中，我们将探索一些调试 CSS 的一般技巧和窍门。
