# 调试环境和工具

每个现代网络浏览器都内置了开发工具，或称为 DevTools。在 [预览](/overview) 章节，我解释了一些关于 Style Master 和 Firebug 工具的内容。浏览器 DevTools 就是基于这些项目开发的。要打开你的开发者工具，右键单击并从菜单中选择"Inspect element"。如果你习惯使用键盘，以下是各个浏览器的快捷键：

- Chrome：Mac `⌥ + ⌘ + I`，Windows `Ctrl + Shift + I`
- Firefox：Mac `⌥ + ⌘ + C`，Windows `Ctrl + Shift + I`
- Safari：`⌥ + ⌘ + I`
- Edge：Mac `⌥ + ⌘ + I`，Windows `Ctrl + Shift + I`

在本书中，除非我提到其他网络浏览器，否则浏览器默认指代 Google Chrome。

你可以检查任何元素并切换其 CSS 属性。要选择一个元素，右键单击并从菜单中选择"Inspect"。

![image-20250324222459019](/img/image-20250324222459019.png)

当你选择"Inspect"时，浏览器的 DevTools 将在屏幕底部打开。这是它的默认位置。你可以通过点击右上角的点图标将其固定到屏幕的右侧或左侧。

![image-20250324222523009](/img/image-20250324222523009.png)

点击点图标后，会打开一个小的下拉菜单。你可以根据偏好选择 DevTools 的固定位置。但是，当你在移动设备和平板电脑尺寸下测试时，推荐将其停靠到右侧。它看起来是这样的：

![image-20250324222547869](/img/image-20250324222547869.png)

## 切换 CSS 声明

我们已经打开了 DevTools 并知道如何访问它们。让我们检查一个元素并在基本级别上操作其 CSS。检查元素后，我们可以使用复选框切换其样式（复选框默认不可见）。

检查元素后，查看"Styles"选项卡。你会注意到，当你将鼠标悬停在 CSS 属性上时，CSS 声明前会出现一个复选框。当取消选中此框时，样式将被禁用且不会应用于元素。

![image-20250324222618529](/img/image-20250324222618529.png)

当你关闭样式时，复选框将可见，为你提供视觉提示表明它已被禁用。

![image-20250324222643787](/img/image-20250324222643787.png)

打开和关闭 CSS 声明类似于注释 CSS。实际上，如果你复制一个关闭了其中一个样式的 CSS 规则并将其粘贴到某处，编辑器将通过用 `/* */` 注释掉来禁用该样式。复制时 CSS 的外观如下：

```css
.menu {
  /* display: block; */
}
```

## 使用键盘递增和递减值

在"Elements"面板中，你可以选择一个包含数字的 CSS 声明，并使用上下箭头键递增或递减值。你也可以手动输入值。

![image-20250324222800069](/img/image-20250324222800069.png)

你还可以按住 `Shift`、`Command` 或 `Alt` 键与上下箭头键一起使用，以设定的间隔更改数字：

- `Shift + up/down`: ±10
- `Command + up/down`: ±100
- `Alt + up/down`: ±0.1

这比使用上下箭头一次更改一个数字要快。

当你更改值并想要退出编辑模式时，你可以执行以下操作之一：

- 点击 CSS 声明旁边的空白区域。
- 按 `Escape` 键。
- 按 `Enter` 键（这会移动到 CSS 规则中的下一个声明）。

## CSS 错误

鉴于 CSS 的性质，当出现拼写错误或属性使用不正确的值时，调试变得更加困难。你不会知道属性名称拼写错误，直到你检查显示 bug 的元素。CSS 的工作方式是浏览器将解析所有声明并忽略无效的声明。将此与 JavaScript 进行比较，在 JavaScript 中，错误会破坏整个脚本，打开浏览器的控制台会显示出了问题。

幸运的是，Firefox 有一个很棒的功能，当你使用没有效果的 CSS 属性时会显示警告。在撰写本文时，此功能仅在 Firefox 中可用。

![image-20250324222830308](/img/image-20250324222830308.png)

希望更多浏览器会跟进！

## DevTools 移动端模式

使用浏览器的 DevTools，你可以测试当前网站的不同视口大小。在本节中，我们将查看与现代浏览器（Chrome、Firefox、Safari、Edge）相关的移动端测试话题。

假设你收到客户或同事的消息说："嘿，页面 X 上的字体大小在移动设备上太小了，无法阅读。我们能做些什么吗？"

从他们的消息中，我们可以确定：

- 字体大小太小无法阅读
- 我们需要在移动视口中测试

我们需要做的第一件事是在浏览器中启动 DevTools，并切换到设备工具栏（在 Chrome 中）。你可以通过点击 DevTools 左上角的移动图标或使用键盘快捷键（`Command + Shift + M`）来访问设备工具栏。从那里，我们可以开始测试不同的尺寸，并最终找到问题的根源。

![image-20250324222857995](/img/image-20250324222857995.png)

其他浏览器，如 Firefox 和 Safari，也有设备模式，但称其为"响应式设计模式"。以下是访问方法：

- Firefox: Tools > Web Developer > Responsive Design Mode
- Safari: Develop > Enter Responsive Design Mode

让我们回顾一下测试时需要记住的一些事项。

## 移动模式不显示水平滚动条

如果元素的宽度大于视口，则会生效水平滚动。尝试随机向左或向右滚动。这可以揭示任何不需要的滚动问题。注意：本书有一整章关于如何破坏布局。

## 滚动到视图

在移动模式下测试网站时，页面通常会很长，不断滚动到你想要检查的元素是不实际的。幸运的是，Chrome 有一个名为"滚动到视图"的功能，它会将页面滚动到你选择的部分。

![image-20250324222922966](/img/image-20250324222922966.png)

## 截图设计元素

有时你需要截取页面的屏幕截图，在线可用的工具并不都很好。Chrome 和 Firefox 内置了截图功能。我特别喜欢 Firefox 的功能，名为"Screenshot Node"，它只会截取所选 HTML 元素的屏幕截图。这非常有用且节省时间。

对于基于 Chromium 的浏览器（Chrome 和 Edge），过程是：

1. 选择元素；
2. 按 `Shift + Command + P`（确保没有浏览器扩展使用此命令）；
3. 输入"capture node screenshot"并按"Enter"

在撰写本书时，Chrome Canary 86 在检查器中支持"capture node screenshot"。它很快就会在 Chrome 中正式可用。

在 Firefox 中截图：

1. 打开 Firefox 的 DevTools
2. 右键单击元素
3. 选择"Screenshot Node"

## 设备像素比

设备像素比（DPR，Device Pixel Ratio）是物理像素与逻辑像素之间的比率。例如，iPhone 5 的 DPR 为 2，因为物理分辨率是逻辑分辨率的两倍。

- 物理分辨率：960 × 640
- 逻辑分辨率：480 × 320

在 Chrome 的 DevTools 的移动模式中，你会找到 DPR 的下拉菜单。有两种基本类型的屏幕：标准和"视网膜"。1x 图像在标准屏幕上看起来不错，但在视网膜屏幕上会看起来像素化。DPR 有三个比率：1x、2x 和 3x。Chrome 将其命名为"设备像素比"，而 Firefox 和 Safari 列出了提到的比率。这里的好处是我们可以测试图像并模拟它们在不同分辨率下的外观。

正如 [Google Developers 所述](https://developer.chrome.com/docs/devtools/device-mode/)：

> 要在标准显示器上模拟此效果，请将 DPR 设置为 2 并通过缩放来缩放视口。2x 资源将继续看起来清晰，而 1x 资源将看起来像素化。

如果你有标准屏幕且 `1x` 图像对你来说看起来不错，可以通过将 DPR 设置为 `2` 或选择 `2x` 作为选项然后放大一次来模拟它在 `2x` 屏幕上的外观。

![image-20250324222957579](/img/image-20250324222957579.png)

一般来说，尽可能使用 SVG。这并不总是可行的，所以如果你使用图像，请为它们提供不同的分辨率。例如，你可以使用 HTML `<picture>` 元素来加载同一图像的不同分辨率和大小。然后浏览器将提供适合屏幕大小的分辨率。

## 切换用户代理

根据 Mozilla Developer Network（MDN）：

> User-Agent 请求标头是一个特征字符串，让服务器和网络对等方识别请求用户代理的应用程序、操作系统、供应商和/或版本。

用户代理使服务器能够识别访问者正在使用的浏览器。每个浏览器都有自己的用户代理。

每个浏览器还允许你测试不同的用户代理。如果你在 Windows 上使用 Chrome，你可以将浏览器切换到"macOS 上的 Safari"。Web 服务器将识别你正在浏览的用户代理。

要调试和检查当前浏览器的用户代理，打开 DevTools 的控制台并输入以下内容：

```js
console.log(navigator);
```

我在 macOS 上使用 Chrome。日志显示此字符串：

> Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.163 Safari/537.36

为什么要调试这个？嗯，有一些重要的用例。考虑这个图：

![image-20250324223030006](/img/image-20250324223030006.png)

我们有一个应用程序的下载按钮，它应该根据用户的操作系统而改变。

另一个用例是在 Chrome 和 Firefox 浏览器中都可用的浏览器扩展：

![image-20250324223049392](/img/image-20250324223049392.png)

更改用户代理的过程将取决于你使用的浏览器：

- Chrome: Network Conditions > 取消选择"Select Automatically" > 选择用户代理
- Safari: Develop > User Agent
- Firefox: 我发现在 Firefox 中有点复杂，所以我推荐使用扩展。

## 调试媒体查询

媒体查询是响应式网页设计的基础。没有它们，网络就不会是今天这个样子。要调试媒体查询，我们需要 DevTools 的强大功能。

首先，检查你正在调试的 CSS（以防你没有编写它）。代码是否使用 `min-width` 媒体查询多于 `max-width`？你是否看到任何 `max-width` 媒体查询？这很重要，因为移动优先设计，你可能听说过。它需要首先为小屏幕编写 CSS，然后为平板电脑和桌面设备等更大的屏幕增强体验。

![image-20250324223115465](/img/image-20250324223115465.png)

在这里，我们有一个导航切换，默认在小屏幕上显示。当视口足够大以显示导航项目时，切换消失。

```css
.nav__toggle {
  /* Shown by default */
}

.nav__menu {
  /* Hidden for mobile */
  visibility: hidden;
  opacity: 0;
}

@media (min-width: 900px) {
  .nav__toggle {
    display: none;
  }

  .nav__menu {
    visibility: visible;
    opacity: 1;
  }
}
```

但是，如果这不是移动优先构建的，那么菜单切换将默认隐藏，并通过 `max-width` 媒体查询显示：

```css
.nav__toggle {
  display: none;
}

@media (max-width: 900px) {
  .nav__toggle {
    display: block;
  }

  .nav__menu {
    visibility: hidden;
    opacity: 0;
  }
}
```

在调试项目时，你需要深入了解这些细节，以了解你正在处理的内容。这将帮助你更快地修复问题并减少不必要的副作用。

要在 Chrome 的 DevTools 中查看媒体查询，你需要选择受其影响的元素：

![image-20250324223202989](/img/image-20250324223202989.png)

注意，当我们选择一个元素时，我们看到它的媒体查询。方便的是，我们可以编辑媒体查询并直接在 DevTools 中测试。上图显示了一个正常情况，没有任何问题。让我们探索与媒体查询相关的最常见错误。

### 不要忘记 Meta Viewport 标签

`viewport` meta 标签告诉浏览器，"嘿，请考虑这个网站应该是响应式的？"将以下内容添加到 HTML 的 head 元素中：

```html
<meta name="viewport" content="width=device-width, initial-scale=1" />
```

### 媒体查询的顺序很重要

媒体查询的一致排序很重要。

```css
@media (min-width: 500px) {
  .nav__toggle {
    display: none;
  }
}

.nav__toggle {
  display: block;
}
```

你能猜测 `.nav__toggle` 元素在宽度超过 500 像素的视口中是否可见吗？答案是肯定的，因为 `.nav__toggle` 的第二个声明覆盖了媒体查询中的声明。

![image-20250324223256681](/img/image-20250324223256681.png)

DevTools 将显示类似于上图的内容。媒体查询中的样式将被删除线划掉，意味着它已被取消或覆盖。那么，解决方案是正确排序它们：

```css
.nav__toggle {
  display: block;
}

@media (min-width: 500px) {
  .nav__toggle {
    display: none;
  }
}
```

## 如果媒体查询不起作用怎么办？

当有人报告错误时，说媒体查询不起作用是不够的。但是，我们可以通过简单的测试检查媒体查询是否起作用。假设我们有这个：

```css
@media (min-width: 500px) {
  .element {
    background: #000;
  }
}
```

要测试媒体查询，我们可以为 `body` 添加背景颜色：

```css
@media (min-width: 500px) {
  body {
    background: red;
  }
}
```

仍然不起作用？然后检查是否：

- 缓存的 CSS 已清除，
- CSS 文件正确链接，
- 媒体查询没有拼写错误且没有缺少闭合大括号。

### 避免双断点媒体查询

一个常见的错误是在两个媒体查询中使用相同的值，一个使用 `min-width`，另一个使用 `max-width`。这通常发生在移动导航中。

```css
@media (max-width: 500px) {
  .nav {
    display: none;
  }
}
@media (min-width: 500px) {
  .nav__toggle {
    display: none;
  }
}
```

乍一看，这些媒体查询可能对你来说看起来不错。但是，99% 的时间，你会忘记测试一个重要的断点：`500px`，两个断点之间的 1 像素间隙。在这个断点，导航和切换都不会可见。

![image-20250324223351992](/img/image-20250324223351992.png)

这 1 像素很难调试，除非在移动模式下手动输入 500px 媒体查询的值。为了防止这个问题，避免在两个媒体查询中使用相同的值。

```css
@media (max-width: 499px) {
  .nav {
    display: none;
  }
}

@media (min-width: 500px) {
  .nav__toggle {
    display: none;
  }
}
```

### 列出媒体查询

在 Chrome 中，你可以根据 CSS 中定义的媒体查询查看页面，而不是根据浏览器中可用的设备列表。

![image-20250324223410895](/img/image-20250324223410895.png)

如你所见，我们有两个条，蓝色条用于 min-width 查询，橙色条用于 max-width 查询。拥有所有媒体查询的更广泛视图对于测试多个查询大小很有用。方便的是，我们可以在源代码中显示媒体查询。右键单击媒体查询，选择"Reveal in source code"，你将被带到该媒体查询的代码行。

![image-20250324223424172](/img/image-20250324223424172.png)

### 垂直媒体查询很重要

响应式网页设计的一个常见错误是仅通过调整浏览器宽度或查看多个移动尺寸来测试。但是，减少和增加视口的高度同样重要。

就像我们有针对视口宽度的媒体查询一样，高度也适用同样的情况。考虑以下示例：

![image-20250324223445451](/img/image-20250324223445451.png)

减少视口高度后，我们发现固定标题占用了屏幕垂直空间的很大一部分。注意内容可用区域有多小。用户会感到恼火，无法轻松使用网站。一个简单的解决方案是仅在有足够垂直空间时固定标题。

```css
/* If the height is 800 pixel or more, the header should be fixed. */
@media (min-height: 800px) {
  .header {
    position: fixed;
    /* Other styles */
  }
}
```

### 不要仅依赖浏览器调整大小

调整浏览器大小来测试响应性是不够的。例如，Chrome 的窗口缩小到 500 像素的宽度。这是不够的。你需要测试低于该值（至少 320 像素）。相反，在 DevTools 的设备模式下测试网站。

## 盒模型

如果我们记住盒模型的任何东西，那应该是网页上的每个元素都是一个矩形框，包含以下一个或多个：位置、边距、边框、内边距和内容。

如果将内边距和边框应用于元素，它们将被添加到其内容框中，除非该元素的 `box-sizing` 属性设置为 `border-box`。进行此更改以避免任何问题。

```css
html {
  box-sizing: border-box;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}
```

![image-20250324223540202](/img/image-20250324223540202.png)

我检查了一个网站的徽标以查看其盒模型。注意你需要打开"Computed"选项卡来查看盒模型。所有框都有标签，除了宽度和高度的框。在调试元素时，查看盒模型非常有用，因为它会显示元素的所有内部和外部间距。

有时，我们可能在没有 CSS 重置文件的情况下构建网页，我们会想知道为什么某些元素有某些间距。查看盒模型是了解正在发生什么的最佳方法。

### CSS 中的一切都是盒子

网页上的每个元素都是正方形或矩形。即使元素显示为圆形或具有圆角边缘，它仍然是矩形框。在调试时，请记住这一点。

![image-20250324223606123](/img/image-20250324223606123.png)

亲自看到这一点的最简单方法是访问你最喜欢的网站并将 `outline` 属性应用于所有内容：

```css
*,
*::before,
*::after {
  outline: solid 1px;
}
```

我们已经标记了页面上的每个元素，包括伪元素（`:before` 和 `:after`）。这样，我们可以看到页面本质上是一些在这里和那里绘制的矩形。

## 计算的 CSS 值

在 CSS 中，无论你使用 `rem`、`em`、`%` 还是视口单位，一切都计算为像素值。即使无单位的 `line-height` 属性也计算为像素值。在某些情况下，查看元素的计算值很重要。

```css
.element {
  width: 50%;
}
```

`.element` 的宽度是 `50%`，但我们如何看到其计算值？好吧，感谢 DevTools，我们可以做到这一点。让我们深入"Computed"选项卡。

![image-20250324223706925](/img/image-20250324223706925.png)

你会看到各个部分都分配了数字，以便更容易解释 DevTools 的这个区域。

1. 这是属性的名称。通常，它的颜色与值不同。

2. 这是 CSS 属性的值。

3. 我们可以展开属性以查看其继承的样式。此元素具有 `font-size: 1.125rem`，并且它从 `html` 元素继承 `1em` 字体大小。

4. 这是预计算值，以及值附加到的元素。

5. 这是 CSS 文件的名称和 CSS 规则的行号。

6. 当悬停在属性值上时，会出现一个箭头。单击它将带你到源 CSS 文件和行号。

7. 此过滤器有助于搜索属性。注意你只能按属性的完整名称搜索。例如，搜索 `grid-gap` 不会显示任何内容，而搜索 `grid-column-gap` 会返回结果。

8. 默认情况下，并非所有 CSS 属性都显示在"Computed"选项卡中。你需要选中此框以查看所有属性。

## 灰色属性

![image-20250324223747376](/img/image-20250324223747376.png)

你会注意到没有明确设置高度的元素可能有灰色的 `height` 属性。

```css
.nav__item a {
  padding: 1rem 2rem;
}
```

`<a>` 元素没有设置高度，但实际上，它的高度是内容和内边距的总和，这是明确高度的替代方案。

这不仅发生在内边距上。下面的示例有两个元素，其中一个是空的。

```html
<div class="wrapper">
  <div class="element">content</div>
  <div class="element"></div>
</div>
```

```css
.wrapper {
  display: flex;
}

.element {
  width: 100px;
  padding: 1rem;
}
```

Flexbox 默认拉伸子项目。因此，项目的高度将相等，即使是空的。如果你检查该元素并检查其 `height` 属性的计算值，你会注意到它是灰色的。

提示：灰色属性意味着其值未明确设置。相反，它受到其他因素的影响，例如元素的内容或内边距，或者作为 flexbox 子项。

## Firefox 的样式编辑器

![image-20250324223814545](/img/image-20250324223814545.png)

Mozilla Firefox 浏览器中的样式编辑器是浏览器中的一种设计应用程序。以下是你可以用它做的一些很棒的事情：

1. 创建新样式表并将其附加到文档。

2. 导入 CSS 文件。

3. 列出文档的所有 CSS 文件，能够通过单击眼睛图标激活和停用它们（类似于在设计应用程序中显示和隐藏图层）。

4. 从列表中保存文件。

5. 列出所选 CSS 文件中的所有媒体查询。活动的将以深色突出显示，非活动的将变暗。你可以跳转到具有媒体查询的代码部分。

6. 单击媒体查询。

我特别喜欢的是你可以隐藏所有 CSS 文件，这相当于禁用 CSS。

此外，能够将 CSS 文件导入页面很有用，开辟了很多可能性。想象一下，你正在为网页布局工作，想要在这里和那里更改一些东西，但不想丢失你的工作。你可以导入新的 CSS 文件，将当前 CSS 复制到其中，然后根据需要编辑它。完成后，你可以在旧 CSS 和新 CSS 之间切换以查看完全不同的布局。

## 没有效果的 CSS 属性

一些 CSS 属性有依赖关系。以下面为例：

```css
.element {
  z-index: 1;
}
```

如果你没有将元素的 `position` 更改为 `static` 以外的任何内容，那么它根本不会影响元素。在调试时很难发现这些问题，因为它们不会破坏布局。它们是静默的。

Firefox 有一个非常有用的功能，当 CSS 属性不影响应用它的元素时显示警告。

![image-20250324223957310](/img/image-20250324223957310.png)

这非常有用，在撰写本文时，仅在 Firefox 中可用。

## Firefox 中的兼容性支持

在检查元素的 CSS 时，你可以看到支持特定功能的浏览器以及浏览器版本。你可以通过悬停在其中一个上查看详细信息。我非常喜欢这个功能，因为它为你提供了关于哪些浏览器需要更仔细测试的提示。

![image-20250324224020758](/img/image-20250324224020758.png)

## 在调整浏览器大小时获取计算值

仅查看元素的计算值是不够的。更有用的是过滤你需要检查的特定属性，然后调整响应式视图包装器的大小以查看值的变化。

```css
.title {
  font-size: calc(14px + 2vw);
}
```

在这里，我们有一个标题，基础字体大小为 `14px` 加上 `2vw`（视口宽度的 2%）。这里是一个解释器：

![image-20250324224046855](/img/image-20250324224046855.png)

我搜索了 `font-size`，然后开始调整左侧的视图大小。这是保持自己与后台发生的事情保持一致的非常有用的方法。

## 使用 JavaScript 获取计算值

通过使用 JavaScript 的 `getComputedStyle` 方法，可以获取特定属性的值。考虑以下示例：

```css
.element {
  font-size: 1.5rem;
}
```

我们使用 `rem` 单位设置了字体大小。要获取计算的像素值，我们会这样做。

```js
let elem = document.querySelector('.element'); /* [1] */
const style = getComputedStyle(elem); /* [2] */
const fontSize = style.fontSize; /* [3] */
```

代码的作用如下：

1. 它选择元素。
2. 它将元素的样式存储在 `style` 变量中。
3. 现在 `style` 变量是一个对象，保存元素的所有样式，我们可以获取属性的计算值。

很酷！如果我们想要检查的属性具有基于视口或百分比的值（例如，`font-size: calc(14px + 2vw)`）怎么办？该字体大小的值会随着每次视口调整大小而改变。

```js
let elem = document.querySelector('h1');
window.addEventListener('resize', checkOnResize);
function checkOnResize() {
  const style = getComputedStyle(elem);
  console.log(style.fontSize);
}
checkOnResize();
```

如你所见，这是相同的概念，但这次使用了 `resize` 事件。这对于跟踪事物非常有用，你甚至可以在页面本身上呈现值：

![image-20250324224129520](/img/image-20250324224129520.png)

## 重新排序 HTML 元素

在 Chrome 的 DevTools 中，你可以单击并拖动元素来重新排序。这对于更改整个页面或组件的结构很有用。重新排序后，我们可以开始测试各种事情，例如：

- flexbox `order` 属性，
- 相邻兄弟组合器（`.item + .item`），
- 具有 `margin-bottom` 或 `margin-top` 的元素。

让我们深入了解重新排序的工作原理。

1. 打开 DevTools。
2. 选择要重新排序的元素。
3. 单击并将元素拖动到你想要的任何位置。

这是你如何拖动 section 元素及其兄弟元素的方法：

![image-20250324224157223](/img/image-20250324224157223.png)

我们也可以重新排序子项目。假设每个 section 都有标题和描述。我们可以在其父级内重新排序它们。

![image-20250324224216426](/img/image-20250324224216426.png)

子元素可以通过多种方式拖动：

- 在其父级内（这只会在同一级别重新排序），
- 在其他父元素之间，
- 在另一个父元素内。

![image-20250324224303804](/img/image-20250324224303804.png)

## 在 DevTools 中编辑元素

在 DevTools 中编辑 HTML 元素有多种方法。在你想要添加类或属性或者可能删除整个元素的情况下，它们非常有用。以下是执行此操作的方法：

- 添加或删除 CSS 类，
- 更改元素类型（例如，`<div>` 到 `<h2>`），
- 添加或删除属性，
- 删除元素。

### CSS 类

要添加、编辑或删除 CSS 类，你可以双击元素的类名，它将变为可编辑。但这是不太推荐的方法。更好的方法是选择元素，然后在打开 DevTools 的情况下单击 `.cls` 标签。被单击后，它将显示与所选元素关联的所有类，我们可以通过选中和取消选中框来添加或删除它们。

![image-20250324224325917](/img/image-20250324224325917.png)

### 基于实用程序的 CSS 网站

如果你正在处理的网站是用基于实用程序的 CSS 构建的，那么在浏览器中调试其 CSS 将与调试具有常规类名的网站不同。

这是一个具有类名的元素：

```html
<div class="card"></div>
```

这是使用基于实用程序的 CSS 的相同元素：

```html
<div class="d-flex flex-column p-2 b-2 rounded hidden"></div>
```

当整个网站都使用实用程序类构建时，调试会有所不同。假设我们想要检查元素并通过在 DevTools 中取消选中框来删除 `display: flex`。如果我们这样做，任何使用 `d-flex` 类的元素都会损坏。原因当然是我们已经从所有其他元素中删除了 `display` 属性。

使用 `.cls` 选项会更好，因为它会列出该元素的所有 CSS 类。

![image-20250324224411463](/img/image-20250324224411463.png)

另一个选项是添加内联 CSS 样式，这将覆盖 CSS 文件中添加的样式。或者你可以双击元素的 class 属性并手动删除你不想要的类。

### 更改元素的类型

假设你有一个 `div` 元素，但想要在不离开 DevTools 的情况下更改其类型。好吧，这是可能的。要更改它，双击元素类型，然后编辑开始标签。

注意：不需要编辑结束标签。DevTools 会自动执行此操作。

![image-20250324224434241](/img/image-20250324224434241.png)

### 添加或删除属性

当你需要添加属性时，选择元素，右键单击，然后选择"Add Attribute"。就这么简单。注意你也可以通过双击元素本身来添加它。

![image-20250324224451606](/img/image-20250324224451606.png)

### 删除元素

要删除元素，按 `Function + Backspace` 键。这将在所有浏览器和所有平台上工作。如果你使用 Chrome，只需按 `Backspace` 键。鼠标是一个替代方案：右键单击所选元素，然后从列表中选择"Delete"。

![image-20250324224507438](/img/image-20250324224507438.png)

### 键盘优势

一些键盘快捷键非常有用并提高生产力：

- 使用上下箭头键在元素之间导航。
- 按右箭头键展开元素，按左箭头键折叠它。
- 当选择元素时，按 `Enter` 编辑 CSS 类名。

## H 键

在 DevTools 中隐藏元素的最快方法是按 H 键，这将为元素添加 `visibility: hidden`。元素占用的空间将被保留。

以这种方式隐藏元素有什么好处？以下是几个用途：

- 如果你有一个元素的子元素没有按预期显示，我们可以使用 `H` 来调查它。
- 如果你需要截屏元素或部分，并且不希望所有细节都在图像中，只需使用 `H` 隐藏不需要的元素。

## 强制元素状态

在 CSS 中，元素可以采用四种状态（伪类）之一：`:visited`、`:focus`、`:hover`、`:active`。幸运的是，我们可以使用 DevTools 调试所有这些状态。

在深入了解如何调试它们之前，让我们回顾一下伪类。

- `:visited` 是链接被点击时的状态。当用户重新访问网页时，该链接应该有不同的状态，以便用户可以知道他们已经访问过它。
- `:focus` 是用户通过键盘导航页面时显示的状态。按钮或链接应该采用清楚表明它处于焦点的样式。
- `:hover` 是元素被鼠标悬停时的状态。
- `:active` 是元素被鼠标点击按下时的状态。

在 CSS 中，伪类的顺序很重要。它应该如下：

```css
a:visited {
  color: pink;
}

a:focus {
  outline: solid 1px dotted;
}

a:hover {
  background: grey;
}

a:active {
  background: darkgrey;
}
```

如果不遵循此顺序，某些样式将被覆盖。正确排序样式以避免问题。

让我们进入有趣的部分。我们如何在 DevTools 中调试这些伪类？好吧，有两种方法。

### 选择元素

右键单击元素或单击左侧的点图标，然后选择"Force State"。从选项列表中，选择要激活的状态。见下图：

![image-20250324224555968](/img/image-20250324224555968.png)

选中框会在左侧的元素上添加蓝点。此视觉指示器显示元素具有强制状态。

### 使用面板

强制元素状态的另一种方法是使用 DevTools 面板。单击 `:hov` 将展开包含所有伪类的列表。每个都有一个复选框，这使得在测试时轻松激活或停用状态。

![image-20250324224613110](/img/image-20250324224613110.png)

### 切换元素状态

我们也可以手动添加伪类：

1. 选择元素。
2. 单击面板中的 `+` 按钮。
3. 将在样式面板中添加新规则。你现在需要做的就是编辑它并添加你想要的伪类。

![image-20250324224628468](/img/image-20250324224628468.png)

## 调试通过 JavaScript 显示的元素

在某些情况下，悬停在元素上会向 DOM 添加子元素。调试这些元素很棘手。它们将在检查器中隐藏，因为你没有主动悬停在它们上面。

问题是如何调试隐藏元素？好吧，有几种方法。

### 元素在 HTML 中吗？

在这种情况下，我们想要调试的元素已经在 HTML 中，但通过 CSS 隐藏，只有在其父元素被悬停时才显示。要调试这个，我们需要做的第一件事是检查父元素。什么是父元素，你问？这应该澄清：

![image-20250324224649740](/img/image-20250324224649740.png)

在这个例子中，我们有一个通过 JavaScript 在悬停时切换的下拉菜单。要调试下拉菜单本身，我们会检查"Products"菜单项，下拉元素应该在其中。从那里，我们可以为元素添加 `display: block` 并开始调试过程。

### 元素在悬停时添加到 HTML 吗？

这更具挑战性。在这种情况下，元素仅在其父元素被悬停时添加到 HTML，并且当用户停止悬停时完全从 HTML 中删除。我们需要 DevTools 的帮助。要调试，我们需要在我们想要调试的东西变得可见后冻结网站。最好的方法是暂停 JavaScript 的执行。

当 JavaScript 执行暂停时，可以跟踪切换菜单的代码。这样，我们可以在元素出现后捕获它，并从那里检查它。

一个重要的线索表明元素在悬停时被添加到 DOM 是其父元素闪烁红色。闪烁意味着这个 DOM 元素正在通过添加或删除子项目或可能修改属性来编辑。

![image-20250324224713358](/img/image-20250324224713358.png)

我们如何暂停 JavaScript 执行？简单：

1. 转到"Sources"面板。
2. 展开"Event Listener Breakpoints"。
3. 向"Keyboard"添加事件监听器。

现在，悬停在你想要调试的元素上。一旦你这样做，按键盘上的任何键，你会注意到应用程序冻结，你想要检查的东西不会消失。随意深入检查！

![image-20250324224728393](/img/image-20250324224728393.png)

## 中断 JavaScript

在 Chrome 和 Firefox 的 DevTools 中，你可以通过以下任何一种方式中断 JavaScript 的执行：

- 子树修改，
- 属性修改，
- 节点删除。

![image-20250324224747629](/img/image-20250324224747629.png)

让我们深入了解每一个。

### 子树修改

这针对所选父级的子项目。如果发生任何 HTML 元素的添加或删除，这被认为是修改。在这种情况下，浏览器将添加断点。

### 属性修改

这监视所选元素属性的任何修改，例如类名和 HTML 属性。例如，对类或样式属性的更改会导致浏览器添加断点，然后通过 JavaScript 显示菜单。

### 节点删除

这是相当明显的。一旦元素从 HTML 中删除，JavaScript 执行将暂停。

JavaScript 执行将暂停。

## 使用 Debugger 关键字

有时，CSS 错误会在某个 JavaScript 函数运行时出现。例如，你可能需要在移动菜单切换后调试它。在这种情况下，`debugger` 关键字可能很有用。

在切换按钮的 JavaScript 中，你会添加以下内容：

```javascript
function showNav() {
  debugger;
  // Add a breakpoint once this function is called.
}
```

一旦调用此函数，浏览器将添加断点。然后，你可以开始调试。

注意，如果浏览器不支持 `debugger` 关键字，这不会有任何效果。

## 格式化源代码以便更容易阅读

当你检查元素并想要从 DevTools 检查其 CSS 文件时，你可能会发现文件已被压缩。这使得它难以阅读。幸运的是，我们有小的"Format Code"功能，它可以快速格式化压缩的代码。

![image-20250324224820087](/img/image-20250324224820087.png)

注意开闭大括号图标。单击一下，所有代码都将被格式化并易于阅读。

## 复制元素的 HTML 及其 CSS

唯一允许你复制元素 CSS 样式的浏览器是 Chrome，尽管它并不完美。在撰写本文时的最新版本 Chrome 81 具有该功能。以下是如何做到的：

1. 右键单击元素。
2. 选择"Copy"。
3. 然后，选择"Copy styles"。就是这样！

![image-20250324224837898](/img/image-20250324224837898.png)

在上图中，注意原始 CSS 和从浏览器 DevTools 复制的 CSS 之间的区别。复制的 CSS 具有继承的样式，如 `box-sizing` 和 `font-family`。此外，奇怪的是，它复制了文档中的所有 CSS 属性！

## 渲染字体

渲染字体是当前用于网页的字体。要调试它们，检查任何文本元素，如标题或段落。在"Computed"选项卡的底部，将有一个名为"Rendered Fonts"的部分。在这里，你可以检查应用于元素的字体。

![image-20250324224902672](/img/image-20250324224902672.png)

此外，如"Computed"部分所述，你可以搜索 `font-family` 并查看其计算值。此外，你可以展开其旁边的箭头并查看负责添加字体的 CSS。

## 检查未使用的 CSS

Chrome 的 DevTools 中的一个有用功能使你能够检查未使用的 CSS。它被称为"Coverage"。以下是如何使用它：

1. 打开 DevTools。
2. 单击点图标，然后选择"More"。
3. 打开"Coverage"面板并点击重新加载图标。

重新加载后，你会看到类似以下内容：

![image-20250324224925702](/img/image-20250324224925702.png)

以红色突出显示的代码块是页面上未使用的代码块，而蓝色的代码块正在使用。此外，它显示了已使用 CSS 的百分比。该功能对于重构 CSS 和检查是否有未使用的样式非常有用。

## 使用 DevTools 进行颜色切换

Chrome 的 DevTools 提供三种类型的颜色系统：hex、RGBa、HSLa。当你为元素选择颜色时，它通常作为十六进制颜色添加。如果你想要该颜色的 RGBa 值而不必使用转换器工具怎么办？好吧，该功能在颜色检查器中可用。

![image-20250324224941531](/img/image-20250324224941531.png)

如果你想要特定的蓝色，设计要求你使用 `50%` 不透明度，将颜色作为十六进制值添加到元素，并且在颜色检查器仍然打开的情况下，单击右侧的双箭头图标。这将在 hex、RGBa 和 HSLa 之间切换，非常方便快速将颜色从一种类型转换为另一种类型。

## 从 DevTools 复制 CSS 到源代码

当你编辑元素的 CSS 时，你可能希望将其复制并粘贴回代码编辑器，而不是重新编写。有多种方法可以做到这一点。

### 直接从内联检查器复制

在下图中，我为元素添加了一些内联样式。注意它们如何添加到 DevTools 中的 `element.style` 选择器。此功能对于 Chrome、Firefox 和 Safari 是相同的。

![image-20250324225003788](/img/image-20250324225003788.png)

现在我们已经添加了这些内联样式，可以将它们复制并粘贴到我们的代码编辑器中。

### 在 Firefox 浏览器中使用 `changes` 功能

Firefox 有一个名为"Changes"的有用功能，显示我们在 DevTools 中所做的更改。它不像版本控制显示两个更改之间的差异。以下是如何使用它：

1. 检查要编辑的元素。
2. 编辑样式。
3. 转到"Changes"选项卡，你将看到你所做的编辑。

![image-20250324225031469](/img/image-20250324225031469.png)

## 调试 Source-Map 文件

当使用 Sass 等预处理器时，渲染的 CSS 文件可能包含链接的 source-map 文件的指令。当你在 DevTools 中检查元素的 CSS 时，你会注意到 CSS 在扩展名为 `.scss` 的文件中。这可能令人困惑。

要从 DevTools 中删除该 `.scss` 文件，你必须关闭 source-map 功能；或者你可以在浏览器中打开"Sources"面板并选择 CSS 文件。然后，你会找到类似这样的内容：

```css
/*# sourceMappingURL=index.css.map */
```

这是一个指令，使浏览器加载 Sass 文件。删除它将完全隐藏 source-map 文件。

## 调试由 CSS 引起的可访问性问题

尽管大多数可访问性问题是由误用的 HTML 引起的，但 CSS 在可访问性中也起作用。在本节中，你将学习在调试 CSS 时要记住的一些事情。

### 为文本提供足够的颜色对比度

太淡而无法阅读的颜色对用户来说是个问题。根据 Web 内容可访问性指南 (WCAG) 2.0，前景色和背景色在 AA 级别应具有 4.5:1 的对比度，在 AAA 级别应具有 7:1 的对比度。

为了实现这一点，使用经过充分测试的颜色。有很好的工具可以让我们的工作更轻松。要检查文本颜色是否可访问，检查元素，然后单击小颜色方块。你会看到对比度数字。

![image-20250324225104444](/img/image-20250324225104444.png)

### 在使用 display: none 隐藏之前三思

错误使用 `display: none` 会阻碍可访问性。它很容易破坏体验。假设你有搜索输入和按钮，设计要求隐藏 `label` 元素。

```html
<label class="visually-hidden" for="search">Search</label>
<input type="search" id="search" />
<button>Show results</button>
```

![image-20250324225132479](/img/image-20250324225132479.png)

`label` 应该在视觉上隐藏，但不能使用 `display: none`。为什么？

1. 你无法使用 `for` 属性将 `label` 绑定到 `input`。
2. 屏幕阅读器无法读取输入的标签。如果你幸运的话，它可能会读取占位符，如果已添加的话。

正确的方法是为标签添加 `visually-hidden` 类。这只会在视觉上隐藏它，因此不会成为可访问性问题。

这个代码片段来自[可访问性项目](https://www.a11yproject.com/posts/how-to-hide-content/)：

```css
.visually-hidden {
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(1px 1px 1px 1px); /* IE6, IE7 */
  clip: rect(1px, 1px, 1px, 1px);
  white-space: nowrap; /* added line */
}
```

### 使用可访问性树

DevTools 中的可访问性面板是一个美丽的面板。它为我们提供了关于元素如何暴露给屏幕阅读器的线索。例如，如果我们选择 `input` 字段并检查可访问性树，它将显示标签（如果可用）和占位符。

修复与此相关的小问题可能产生巨大影响，你不需要成为可访问性专家就能做到。这是一个现实的例子：

```html
<label class="visually-hidden" for="email">Email address</label>
<input type="search" placeholder="Email address" id="email" />
```

我们有一个电子邮件 `input`，没有与之关联的 `label`。如果我们检查 `input` 并转到可访问性面板，我们会看到类似这样的内容：

![image-20250324225207380](/img/image-20250324225207380.png)

注意它说"textbox: Email address"，它读取 `input` 占位符内的内容。没有它，字段将为空，这将是一个问题。在处理 Web 表单时，确保使用可访问性树进行调试。

当然，这不仅仅是关于表单。有些元素不应该暴露给屏幕阅读器用户——例如，带有伴随箭头的菜单项。

![image-20250324225225837](/img/image-20250324225225837.png)

箭头是 span 内的 HTML 符号。检查时，它将文本显示为"Services ▼"，这是不正确的。屏幕阅读器会读作："Services down pointing black pointer"。非常令人困惑。强烈建议尽早调试此类问题。解决方案是为 span 元素使用 `aria-hidden=true`。

### 修复不可点击的元素

与按钮和链接的交互至关重要。当元素预期可点击但不可点击时，这是一个问题。误用 CSS 属性可以阻止元素交互。考虑这个例子：

```css
.element {
  pointer-events: none;
}
```

CSS `pointer-events` 阻止，例如，按钮上的事件发生。在这种情况下，当用户悬停在它上面时：

- 光标不会改变，
- 点击它什么也不做。

一个简单的 CSS 属性可以阻止按钮被点击。误用属性可能破坏体验，导致用户流失。

## 调试 CSS 性能

某些 CSS 属性在用于动画时如果使用不当可能会导致性能问题。任何浏览器都可以廉价动画的属性是：

- 变换（`translate`、`scale`、`rotate`）；
- 不透明度。

使用其他属性进行动画是有风险的，可能会损害性能。让我们回顾一下浏览器如何计算其样式。以下是浏览器采取的步骤：

1. **重新计算样式**：计算应用于每个元素的样式。
2. **布局**：分配每个元素的宽度、高度和位置。
3. **绘制**：将所有元素绘制到图层。
4. **合成图层**：将图层绘制到屏幕。

最轻的步骤是合成。要获得良好的性能，只使用 `transform` 和 `opacity` 属性。下图比较了用于动画的 `left` 和 `transform: translateX`。

![image-20250324225253410](/img/image-20250324225253410.png)

注意 `left` 时间线有多忙。浏览器在动画发生时不断重新计算样式。同时，`transform: translateX` 非常不同；浏览器的工作很轻。

要检查网页的性能，打开 DevTools，然后选择"Performance"选项卡。从那里，你可以开始分析和进行测试。配置文件就像在页面上运行一段时间（通常是几秒钟）的测试。完成后，你可以看到时间线，其中包含浏览器如何计算样式的所有详细信息。

我们对 CSS 的关注是重新计算和合成。避免使用重型 CSS 属性进行动画。

## 多个浏览器配置文件

你可能使用不同的浏览器，每个浏览器都存储你浏览的历史记录和私人信息。在你每天使用的浏览器中调试和测试网站可能没有意义。你需要一些新鲜的东西，没有历史记录或缓存，这样你就可以在没有任何不必要问题的情况下进行测试，比如 CSS 缓存，并避免可能导致错误的扩展。

因此，建议使用专用的浏览器配置文件进行测试。以下是在 Chrome 中创建一个的方法：

1. 在右上角，单击用户头像。
2. 单击"Add +"。
3. 命名配置文件（例如，"Dev"），然后单击"Add"。

在 Firefox 中，它有点不同：

1. 在浏览器的 URL 字段中打开 `about:profiles`。
2. 单击"Create a new profile"。
3. 选择名称并单击"Done"。
4. 在同一页面上，向下滚动找到你创建的配置文件，然后单击"Launch profile in a new browser"。

完成！你已经创建了一个专门用于测试和调试的配置文件。

## 渲染和模拟

在 Chrome 中，我们可以模拟不同的渲染和模拟媒体查询，以帮助我们调试 CSS 查询 `@media print`。我们还可以使用 `prefer-color-scheme` 媒体查询调试网站的浅色和深色模式版本。

要访问渲染和模拟设置，请按照以下步骤操作：

1. 打开 DevTools，然后单击垂直点菜单。
2. 选择"More tools"，然后选择"Rendering"。
3. 向下滚动，你会找到模拟选项。

### CSS 打印样式

![image-20250324225325779](/img/image-20250324225325779.png)

我们可以使用媒体查询来编辑 CSS 样式并定制要打印的页面。要调试和模拟网页打印时的外观，我们可以打印页面并将其保存为 PDF 文件，或者使用 Chrome 中的模拟功能。

```css
@media print {
  /* All of your print styles go here. */
  .header,
  .footer {
    display: none;
  }
}
```

网站的页眉和页脚可能不需要打印，因此可以使用打印媒体查询隐藏它们。

### CSS 媒体 `prefer-color-scheme`

![image-20250324225350017](/img/image-20250324225350017.png)

随着 iOS 正式支持它们，深色模式用户界面越来越受欢迎，并且在网络上也得到支持。在 CSS 中，我们可以使用以下内容来检测用户是否喜欢深色或浅色模式：

```css
.element {
  /* Light-mode styles (the default) */
}
@media (prefer-color-scheme: dark) {
  .element {
    /* Dark-mode styles */
  }
}
```

在 macOS 上，你可以通过更改系统偏好设置在网站的深色和浅色模式之间切换。

![image-20250324225404691](/img/image-20250324225404691.png)

虽然这有效，但当你在进行大量更改时可能不实用。幸运的是，可以在渲染设置中测试。我们可以模拟媒体查询 `prefer-color-scheme: dark` 或 `prefer-color-scheme: light`。

### CSS 媒体 `prefers-reduced-motion`

![image-20250324225419045](/img/image-20250324225419045.png)

你不能假设所有用户都能接受在你的网站上播放动画。有些人更喜欢不在页面上看到动画，因为它可能影响可访问性。媒体查询检查用户是否已请求系统最小化非必要运动。

```css
.element {
  animation: pulse 1s linear infinite both;
}
@media (prefers-reduced-motion: reduce) {
  .element {
    animation: none;
  }
}
```

更好的是，你可以为喜欢减少运动的用户提供更简单的动画。

```css
@media (prefers-reduced-motion: reduce) {
  .element {
    animation-name: simple;
  }
}
```

有了这个，我们就完成了对浏览器 DevTools 的回顾。让我们回顾一下我们可以用来测试和调试 CSS 的其他方法。

## 虚拟机

在你作为 Web 开发人员的工作过程中，你需要在除了你通常使用的浏览器和操作系统之外的浏览器和操作系统上进行测试。例如，你可能使用 macOS 但想要在 Windows 的 Chrome 上测试。在这种情况下，最便宜的解决方案是使用虚拟机。我推荐使用 VirtualBox，因为它是免费的，易于使用，并且在 macOS 和 Windows 上都可以工作。

此外，Microsoft 提供免费的 Windows 副本，用于在 Edge 和 Internet Explorer 11 浏览器中测试。

## 在线服务

类似于虚拟机，一些在线服务使你能够在数百种类型的设备上进行测试。但是，它们不是免费的，并且依赖于快速的互联网连接。

## 移动设备

在真实移动设备上测试无法与使用浏览器 DevTools 测试相比。原因是无法在 DevTools 中模拟真实设备。触摸屏本身在测试网站中起着巨大作用。

我建议，如果可行的话，购买两部 Android 手机作为测试设备。每台设备投资不超过 150 美元。另一个解决方案是使用你家人的手机。我总是借用我妈妈的手机在 Android 上仔细检查。

**提示**：如果你的 Web 项目在 `localhost` 上运行，你可以在连接到同一 Wi-Fi 网络的任何移动设备上打开其链接。例如，如果项目在 `localhost:3000` 上运行，以下是 macOS 用户如何获取完整 IP 地址的方法：

1. 转到"System Preferences"，然后转到"Network"。
2. 在"Connected"部分，记下 IP 地址（我的是 `*************`）。
3. 在你的移动设备上，输入带有端口号的地址（我的是 `*************:3000`）。

然后，你可以在移动设备上访问项目。从那里，你可以更新和编辑 CSS 来测试它。

## 移动浏览器

移动设备上的浏览器与我们在桌面上使用的浏览器不同。它们更简单、更轻量。在 iOS 上，默认浏览器是 Safari。在 Android 上，这取决于手机制造商。例如，三星手机有一个名为 Samsung Internet 的预装浏览器。

## 检查你的移动浏览器

通过 USB 或 USB-C 将手机连接到计算机，你可以检查代码。对于 iOS，我们可以连接 iPhone，然后在计算机上使用 Safari 检查它。这使得检查和测试更快。对于 Android 设备，过程更复杂。请参考 Chrome DevTools 博客的[这个很棒的资源](https://developers.google.com/web/tools/chrome-devtools/remote-debugging)。

## 移动模拟器

macOS 开发人员可以访问 iOS 模拟器，在那里他们可以在多个 iOS 设备尺寸（iPhone、iPad）上进行测试。此外，可以为你测试的每个设备打开 DevTools。

## 浏览器支持

在开始新的前端项目时，决定你想要支持的浏览器。例如，你会支持 Internet Explorer 11 吗？或者旧版本的 Firefox？提前回答这些问题，以便为即将到来的事情做好准备。

在开发过程中，你可能会意外使用在你想要支持的浏览器中不受支持的 CSS 功能。因为你是根据渐进增强进行设计的，你需要检查 CSS 功能是否受支持，如果是，那么你会将该功能作为增强应用。

诸如 [doiuse](https://github.com/anandthakker/doiuse) 之类的工具可以通过 npm 安装在你的项目中。告诉它要支持的最低浏览器。下面的示例命令将在命令行中运行：

```bash
doiuse --browsers "ie >= 9, > 1%, last 2 versions" main.css
```

输出将列出 CSS 功能以及警告——例如，属性 X 仅在 Internet Explorer 11 及以上版本中受支持。

## Can I Use

Can I Use 是一个非常有用的工具，用于搜索特定的 CSS 功能。它会告诉你功能支持的历史。有时，属性的支持表会在修复问题时为你节省数小时的试错时间。

## 供应商前缀

浏览器供应商为仍未最终确定的实验性 Web 功能添加前缀。理论上，开发人员不应该在生产网站上使用这些属性，直到它们得到 100% 支持；然后，他们可以使用无前缀版本。但是，许多开发人员没有足够的耐心等待数年才能完全支持属性。

根据 MDN：

> 浏览器供应商正在努力停止对实验性功能使用供应商前缀。Web 开发人员一直在生产网站上使用它们，尽管它们具有实验性质。这使得浏览器供应商更难确保兼容性和处理新功能；对于较小的浏览器来说，这也是有害的，它们最终被迫添加其他浏览器的前缀以加载流行的网站。

这意味着你不会看到未来 CSS 功能的任何供应商前缀。这很好；它将使新功能的发布速度更快。

MDN 补充说：

> 最近，趋势是在用户控制的标志或偏好设置后面添加实验性功能，并创建可以更快达到稳定状态的较小规范。

MDN 列出了所有主要浏览器的前缀：

- `-webkit-`：Chrome；Safari；Opera 的最新版本；几乎所有 iOS 浏览器，包括 iOS 的 Firefox；基本上，任何基于 WebKit 的浏览器
- `-moz-`：Firefox
- `-o-`：Opera 的旧预 WebKit 版本
- `-ms-`：Internet Explorer 和 Microsoft Edge

以下是供应商前缀的示例用法：

```css
-webkit-transition: all 4s ease;
-moz-transition: all 4s ease;
-ms-transition: all 4s ease;
-o-transition: all 4s ease;
transition: all 4s ease;
```

在开发网站时手动添加这些前缀是不实用的。名为 Autoprefixer 的工具会自动为你完成。指定要支持的浏览器，它会完成其余工作。

## 总结

在本章中，我们涵盖了调试环境（如 DevTools）、虚拟机、移动设备和在线服务。掌握 DevTools 的每个细节将大大减少你在解决和调试 CSS 上花费的时间。

接下来，我们将探索一些常见的 CSS 问题并学习如何解决它们。准备好了吗？
