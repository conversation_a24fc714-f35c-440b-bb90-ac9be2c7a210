# CSS Bug 简介

## 什么是 CSS Bug？

当某些内容与你的预期不符时，那就是一个 bug。例如，一个图标可能没有与其兄弟元素对齐，或者一张图片可能看起来很奇怪，因为它被拉伸了（其宽度和高度不成比例）。在某些情况下，你认为的 bug 实际上可能是预期的行为或功能需求。因此，值得仔细检查并详细询问相关人员关于这个 bug 的具体情况。

### 浏览器之间的差异

Web 浏览器各不相同，并非所有浏览器都支持 CSS 中的所有功能。在你的工作过程中，你可能会遇到在浏览器 X 中看起来像 bug 的问题，而在浏览器 Y 中却工作得很完美。这并不意味着浏览器 X 的实现有误。有时，问题的出现是因为一个浏览器厂商按照规范实现了某个功能，而在另一个浏览器中工作是因为该厂商没有正确实现它。

# 如何修复 CSS Bug

当你发现 CSS bug，或团队成员指出你负责的页面存在问题时，请遵循以下基本步骤。

### 检查 CSS

首先，检查正在使用的 CSS。你是否使用了一些只在现代浏览器中支持的前沿属性？或者是一些旧的、在出现 bug 的浏览器中应该能正常工作的属性？

它在你日常使用的主力浏览器中是否表现正常？又或者该站点由他人编写，你尚未验证其在本地浏览器中的表现？若属后者，首先确认问题能否在你的浏览器中复现。

### 检查浏览器支持

访问 [Can I Use](https://caniuse.com/) 并检查 CSS 属性的浏览器支持情况。如果你发现该属性在出现 bug 的浏览器中不受支持，或者该属性只有在使用厂商前缀时才受支持，那么这可能就是问题的答案。确保添加了厂商前缀（如果需要的话），并且你正在测试的浏览器支持该 CSS 属性。

### 使用浏览器的开发者工具

一旦你确定 CSS 属性可以在出现 bug 的浏览器中正常工作，那么就是时候深入研究浏览器的开发者工具（DevTools）了。在检查元素之前，你需要确定这是什么类型的问题。例如：

- 这是一个视觉问题，比如图标未对齐吗？
- 它是发生在特定部分还是跨页面发生？（问题可能与 CSS 布局有关。）

在下一节中，我们将深入了解我们发现的 CSS 问题类型的详细信息，以及如何使用浏览器的 DevTools 来调试它们。

## CSS Bug 类型

按类型对 bug 进行分类是有帮助的。例如，问题是与设计相关还是与语法错误相关？在本章中，我们将逐一介绍每种类型，并提供基本示例。

### 视觉设计 Bug 类型

当你在 HTML 和 CSS 中实现设计时，设计和代码之间任何明显的不一致都可以被认为是 bug。例如，你是否注意到图标与其文本标签未对齐，或者页面容器比设计中提议的更宽或更窄？所有这些都可以被认为是开发者无意中造成的视觉设计问题。

设计师可能不了解 CSS，在这种情况下，他们可能会截取问题的屏幕截图，并将其发送回开发者并附上注释。如果开发者有设计背景，那么他们可能能够轻易注意到设计师报告的那些不一致之处。

考虑以下图片：

![image-20250325001053823](/img/image-20250325001053823.png)

在上面显示的导航设计中，第一个是原始设计，第二个是代码实现。开发者努力实现它，但由于几个原因，它仍然与原始设计相差甚远：

- 高度更短。
- 字体大小更小。
- 边框半径更小。
- 边框颜色不同。
- 阴影太浅。

我们已经发现了实现与设计不相似的五种方式。在更大的规模上，许多组件和部分需要精心制作，以使实现看起来与设计相似。并非所有开发者都注意到这些设计细节。

此外，任何虽非技术层面 bug 却会妨碍用户体验的视觉问题，也属此类。一些例子是不符合可访问性要求的配色、令人困惑的内容组织、未对齐的按钮、导致布局错乱的文本换行，以及网站页面之间的不一致行为。所有这些都会导致视觉设计问题，进而导致可访问性问题。

### 技术 Bug 类型

并非所有问题都只是通过查看网页就能注意到。有时你处理的是语法错误或 CSS 属性的不正确值。让我们探索技术问题的原因。

#### 引用错误的文件路径

许多开发者花费数小时试图弄清楚为什么某些 CSS 根本不工作，结果发现原因是 CSS 文件的路径不正确。这可能发生是因为你使用了 CSS 预处理器，如 Sass 或 LESS，它会编译生成一个 `.css` 文件。有时，编译后的文件名称与源文件的名称不同。始终确保引用的 CSS 文件是正确的，特别是如果你有多个 CSS 文件。

#### 属性名称拼写错误

当你在 CSS 属性名称中打错字时，浏览器不会直接告诉你。CSS 在出现错误时不会抛出错误。你需要通过使用浏览器的 DevTools 来发现。如果你检查元素，浏览器会显示无效属性，并带有警告三角形和名称上的删除线。

我记得为一篇文章制作一个简单的演示时，我绞尽脑汁试图弄清楚为什么某些东西不工作？结果发现我在声明 opacity 属性时有一个拼写错误。

```css
.element {
  opacity: 0.5;
}
```

我没有注意到这个微不足道的错误的原因是我太分心了，没有静下心来思考 bug 的原因。大多数代码编辑器会在属性名称拼写错误时警告你。以下是来自 Visual Studio Code 的示例：

![image-20250324222007023](/img/image-20250324222007023.png)

#### 为属性使用无效值

与上一个问题类似，当你给 CSS 属性一个无效值时会发生这种情况。该值可能是拼写错误或不适用于给定属性的值。考虑以下示例：

```css
.element {
  opacity: 50;
}
```

`opacity` 属性接受从 0 到 1 的值。这里的作者想要 50% 的不透明度，但将其表示为数字 50，同时忘记了百分号。浏览器会将无效值视为未声明，从而忽略该属性。

#### 使用依赖于另一个属性的属性

并非所有属性都能独立工作。一个属性可能依赖于应用于元素本身或父元素或子元素的某个规则。考虑这个：

```css
.element {
  z-index: 1;
}
```

`z-index` 属性不会工作，因为它需要一个除 `static` 之外的 `position` 值。浏览器不会将此标记为无效，你需要猜测为什么它不工作。

让我们考虑何时必须将规则应用于父元素或子元素：

```css
.child {
  position: absolute;
}
```

我们希望子元素相对于其父元素绝对定位。但是，父元素没有 `position: relative`。这将导致子元素根据最近的相对定位的父元素或 body 元素进行定位。

#### 用一个属性覆盖另一个属性

有时没有拼写错误或错误，但你用一个属性覆盖了另一个属性。这就是 CSS 的工作方式，但一些开发者可能认为发生了 bug。例如，CSS 的最小和最大尺寸属性可能令人困惑。

```css
.element {
  width: 100px;
  min-width: 50%;
  max-width: 100%;
}
```

这里，元素的宽度将是其父元素的 `50%`。如果你没有仔细阅读 CSS 规范，你可能认为这是一个问题，但它不是。

#### 重复属性

有时你会声明一个属性，由于某种原因它对元素没有影响。你不断尝试和测试，但没有结果。最终，你意识到属性是重复的，你正在编辑它的第一个声明，而它被第二个声明覆盖了。

```css
.element {
  display: block;
  width: 50%;
  opacity: 1;
  border: 1px solid #ccc;
  opacity: 0;
}
```

这里 `opacity` 属性被定义了两次。这是一个错误，可能由于各种原因发生：

- 也许你复制了一些样式来快速测试它们，然后忘记删除重复的。
- 这可能只是意味着你累了，需要休息一下。

无论原因是什么，这都是一个 bug。

#### 类名拼写错误

你的 CSS 可能 100% 正确和有效，但类名中的一个拼写错误可能导致样式不被应用到元素上。尽管这很简单，但当我们每天工作八小时时，我们倾向于专注于大问题，可能会忽略这样的小错误。

#### 忽略层叠

CSS 代表层叠样式表。正如名称所示，网站的样式是层叠的，它们的顺序很重要。如果你为一个元素定义了一个 CSS 规则，然后在 CSS 文件的末尾重新定义它，后者将覆盖前者。

```css
.element {
  color: #000;
}
/* 500 行之后… */
.element {
  color: #222;
}
```

这是一个可能发生的非常简单的例子。你可能面临比这更棘手的问题。考虑一个应该在移动设备和桌面设备上切换颜色的元素：

```css
@media (min-width: 500px) {
  .element {
    background: #ccc;
  }
}

.element {
  background: #000;
}
```

`.element` 的背景颜色将是 `#000`，因为它在媒体查询规则之后（因此覆盖了它）。

#### 忘记清除缓存

CSS 常被浏览器或 CDN 缓存，而非直接存储在你的本地项目目录中。一个常见问题是推送更新后，当你刷新网页时，CSS 更新没有出现。在这种情况下，CSS 文件可能被缓存了，你需要清除浏览器的缓存或在每次推送后重命名文件。

这个问题有多种解决方案，最简单的是添加查询字符串：

```html
<link rel="stylesheet" href="app.css?v=1.0.0" />
```

当你进行更改时，你也会更改版本：

```html
<link rel="stylesheet" href="app.css?v=1.0.1" />
```

然后，浏览器会下载最新的 CSS 文件。也可使用基于内容哈希的文件名自动化缓存破坏策略，以避免人工维护版本号。有关更多信息，CSS-Tricks 有[一篇很好的文章](https://css-tricks.com/strategies-for-cache-busting-css/)。

#### 忽略性能

为工作使用错误的属性很容易损害性能。例如，当将元素从左到右动画时，`left` 属性是性能杀手，因为它强制浏览器在每个像素移动时重新绘制布局。

```css
.element:hover {
  left: 100px;
}
```

更好的解决方案是使用 CSS `transform` 属性。它不会影响性能，一切都会顺利运行。属性的简单选择可以显著提高性能！

```css
.element:hover {
  transform: translateX(100px);
}
```

#### 忽略特异性

如果 CSS 规则没有按预期工作，原因可能是它的特异性比另一个更高。

```css
.title {
  color: #222;
}

.card .title {
  color: #000;
}
```

`.card .title` 的特异性比 `.title` 更高。结果，前者会被应用。为了解决这个问题，我们可以向元素添加一个变体类，并将新颜色应用于该类。

```css
.card-title {
  color: #000;
}
```

另一种可能性是使用 `!important`。一般来说避免使用这个，因为它使大规模维护 CSS 变得更加困难。明智地使用它，只在需要时使用。

## 调试过程

正如我们所见，CSS 问题有很多类别。有些是视觉的，有些是非视觉的。现在我们已经完成了常见类型的列举，下一步是弄清楚如何使用我们可用的各种工具和技术进行调试。

## 从非技术人员那里获取浏览器信息

假设用户报告了你网站上的问题。作为前端开发者，你检查了自己的浏览器，一切都正常。所以，问题只出现在用户的浏览器中。在这种情况下，向非技术人员询问更多详细信息的最佳方式是什么？以下是你通常会采取的步骤：

1. 询问浏览器的名称。
2. 询问浏览器版本，并解释用户如何获取它（例如，点击设置图标，然后点击 `关于`，并复制底部的数字）。
3. 询问完整页面的屏幕截图。如果用户不知道如何做到这一点，向他们推荐一个易于使用的浏览器扩展。

获取浏览器信息的一个很好的工具是 Andy Bell 的 [mybrowser.fyi](https://mybrowser.fyi/)。很棒的部分是用户可以分享他们浏览器信息的自动生成链接。以下图片显示了视觉结果：

![image-20250324222310444](/img/image-20250324222310444.png)

一旦你有了浏览器的名称和版本并获得了问题的视觉效果，你就可以开始调试。如果你没有需要调试的浏览器，那么你可以在你的机器上安装它或使用在线服务，如 BrowserStack。

### 调试技术

当涉及到测试网页以调试 CSS 时，我们可以使用很多技术和工具，最常见的是：

- 浏览器的 DevTools；
- 移动设备；
- 移动模拟器（如 iOS 模拟器）；
- 虚拟机（如 VirtualBox）；
- 在线服务（如 BrowserStack 和 CrossBrowserTesting）。

## 总结

在本章中，我们定义了什么是 bug，介绍了不同类型的 CSS bug，并总结了调试过程。在下一章中，我们将深入研究浏览器的 DevTools，并学习如何在修复 CSS 问题时利用它们。
