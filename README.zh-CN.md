# CSS Debug Master

[English](README.md) | [中文](README.zh-CN.md)

CSS 调试技巧与最佳实践的综合性指南和文档。

<img src="./logo.png" alt="CSS Debug Master" style="width: 100%" />

## 项目概述

本项目作为 CSS 调试的知识库，提供详细的解释、示例和常见 CSS 问题的解决方案。内容来源 [Debugging CSS](https://debuggingcss.com/)，本网站内容**仅用于学习交流，不用于商业用途**。

## 主要特点

- CSS 调试技术的详细文档
- CSS 开发的最佳实践
- 常见陷阱及其解决方案
- 交互式示例和演示

## 快速开始

1. 克隆仓库

   ```bash
   git clone https://github.com/chenmijiang/css-debug-master.git
   ```

2. 安装依赖：

   ```bash
   bun install
   ```

3. 运行开发服务器：

   ```bash
   bun run dev
   ```

## 参与贡献

欢迎通过以下方式参与项目：

- 提交 bug 报告或功能建议
- 提交改进的 Pull Request
- 建议新的主题或示例

## 联系方式

如有问题或建议，请在 GitHub 上[提交 issue](https://github.com/chenmijiang/css-debug-master/issues)。
