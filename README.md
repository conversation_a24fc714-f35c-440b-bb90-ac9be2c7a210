# CSS Debug Master

[English](README.md) | [中文](README.zh-CN.md)

A comprehensive guide and documentation for CSS debugging techniques and best practices.

<img src="./logo.png" alt="CSS Debug Master" style="width: 100%" />

## Overview

This project serves as a knowledge base for CSS debugging, providing detailed explanations, examples, and solutions for common CSS issues and challenges. Content source from [Debugging CSS](https://debuggingcss.com/), this website content **is only for learning and communication, not for commercial use**.

## Features

- Detailed documentation on CSS debugging techniques
- Best practices for CSS development
- Common pitfalls and their solutions
- Interactive examples and demonstrations

## Getting Started

1. Clone the repository

   ```bash
   git clone https://github.com/chenmijiang/css-debug-master.git
   ```

2. Install dependencies:

   ```bash
   bun install
   ```

3. Run the development server:

   ```bash
   bun run dev
   ```

## Contributing

Feel free to contribute to this project by:

- Opening issues for bugs or feature requests
- Submitting pull requests with improvements
- Suggesting new topics or examples

## Contact

For questions or suggestions, please [open an issue](https://github.com/chenmijiang/css-debug-master/issues) on GitHub.
